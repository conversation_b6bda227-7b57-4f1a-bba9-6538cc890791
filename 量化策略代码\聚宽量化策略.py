# 聚宽量化策略 - 基于四层级板块管理的多层级决策体系
# 融合郭海培老师《王者之声》和《胜者为王》交易体系

import pandas as pd
import numpy as np
from jqdata import *
import datetime
import talib

def initialize(context):
    """初始化函数"""
    # 设置基准
    set_benchmark('000300.XSHG')
    # 设置滑点
    set_slippage(FixedSlippage(0.02))
    # 设置交易成本
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, open_commission=0.0003, close_commission=0.0003, close_today_commission=0, min_commission=5), type='fund')
    # 开启动态复权模式
    set_option('use_real_price', True)
    # 设置避免未来数据
    set_option('avoid_future_data', True)

    # 策略参数
    g.stock_num = 10  # 持股数量
    g.max_position_pct = 0.1  # 单票最大仓位
    g.stop_loss_pct = 0.08  # 止损比例
    g.take_profit_pct = 0.15  # 止盈比例

    # 四层级板块管理
    g.strong_sectors = []  # 强势指数板块1
    g.sector_stocks = []   # 强势板块中的股票2
    g.strong_stocks_in_sector = []  # 板块中的强势股3
    g.strong_stocks_market = []     # 强势个股4

    # 委托管理
    g.pending_orders = {}  # 待处理订单
    g.order_retry_count = {}  # 订单重试次数
    g.max_retry = 3  # 最大重试次数

    # 黑名单管理
    g.blacklist = []  # 黑名单股票
    g.just_sold = []  # 刚卖出的股票

    # 定时任务
    run_daily(market_risk_check, time='9:05')  # 市场风险判断
    run_daily(update_four_level_sectors, time='9:10')  # 更新四层级板块
    run_daily(check_positions, time='9:30')  # 检查持仓
    run_daily(check_limit_up, time='14:00')  # 检查涨停
    run_daily(adjust_positions, time='14:50')  # 调仓

    # 委托状态检查（每3秒，交易时间内）
    run_daily(check_order_status, time='9:30')
    run_daily(check_order_status, time='9:33')
    run_daily(check_order_status, time='9:36')
    run_daily(check_order_status, time='9:39')
    run_daily(check_order_status, time='9:42')
    run_daily(check_order_status, time='9:45')
    run_daily(check_order_status, time='9:48')
    run_daily(check_order_status, time='9:51')
    run_daily(check_order_status, time='9:54')
    run_daily(check_order_status, time='9:57')
    # 继续添加更多时间点...

def market_risk_check(context):
    """市场风险判断 - 基于沪深300和富时A50期指"""
    # 获取沪深300指数数据
    hs300_data = get_price('000300.XSHG', count=60, end_date=context.previous_date, fields=['close'])
    hs300_ma60 = hs300_data['close'].rolling(60).mean().iloc[-1]
    hs300_current = hs300_data['close'].iloc[-1]

    # 获取富时A50期指数据（如果可用）
    # a50_data = get_price('CN0Y.XSGE', count=60, end_date=context.previous_date, fields=['close'])

    # 市场风险判断
    if hs300_current > hs300_ma60:
        if hs300_ma60 > hs300_data['close'].rolling(60).mean().iloc[-2]:
            g.market_risk = 'low'  # 低风险，仓位60-100%
            g.max_position = 1.0
        else:
            g.market_risk = 'medium'  # 中等风险，仓位30-60%
            g.max_position = 0.6
    else:
        g.market_risk = 'high'  # 高风险，仓位0-20%
        g.max_position = 0.2

    log.info(f"市场风险等级: {g.market_risk}, 最大仓位: {g.max_position}")

def update_four_level_sectors(context):
    """更新四层级板块管理"""
    # 1. 强势指数板块1 - 筛选强势板块指数
    g.strong_sectors = identify_strong_sectors(context)
    log.info(f"强势指数板块1: {len(g.strong_sectors)}个")

    # 2. 强势板块中的股票2 - 获取强势板块对应的所有股票
    g.sector_stocks = []
    for sector in g.strong_sectors:
        sector_stocks = get_industry_stocks(sector)
        g.sector_stocks.extend(sector_stocks)
    g.sector_stocks = list(set(g.sector_stocks))  # 去重
    log.info(f"强势板块中的股票2: {len(g.sector_stocks)}只")

    # 3. 板块中的强势股3 - 从板块股票中筛选真正强势个股
    g.strong_stocks_in_sector = identify_strong_stocks_in_sectors(context, g.sector_stocks)
    log.info(f"板块中的强势股3: {len(g.strong_stocks_in_sector)}只")

    # 4. 强势个股4 - 全市场强势股票池
    g.strong_stocks_market = identify_strong_stocks_market(context)
    log.info(f"强势个股4: {len(g.strong_stocks_market)}只")

def identify_strong_sectors(context):
    """识别强势板块指数"""
    strong_sectors = []

    # 获取所有行业
    industries = get_industries('sw_l1')

    for industry_code, industry_name in industries.items():
        try:
            # 获取行业指数数据
            industry_data = get_price(industry_code, count=20, end_date=context.previous_date,
                                    fields=['close', 'volume'])

            if len(industry_data) < 20:
                continue

            # 计算涨幅
            return_1d = (industry_data['close'].iloc[-1] / industry_data['close'].iloc[-2] - 1) * 100

            # 计算成交量放大倍数
            volume_ratio = industry_data['volume'].iloc[-1] / industry_data['volume'].rolling(5).mean().iloc[-1]

            # 计算5日均线
            ma5 = industry_data['close'].rolling(5).mean()

            # 强势板块标准
            if (return_1d > 2.0 and  # 涨幅大于2%
                volume_ratio > 1.5 and  # 成交量放大50%以上
                industry_data['close'].iloc[-1] > ma5.iloc[-1] and  # 站稳5日均线
                ma5.iloc[-1] > ma5.iloc[-2]):  # 5日均线上行

                strong_sectors.append(industry_code)

        except Exception as e:
            continue

    return strong_sectors

def identify_strong_stocks_in_sectors(context, sector_stocks):
    """从板块股票中识别强势个股"""
    strong_stocks = []

    # 基础过滤
    sector_stocks = filter_stocks(context, sector_stocks)

    for stock in sector_stocks:
        try:
            # 获取股票数据
            stock_data = get_price(stock, count=20, end_date=context.previous_date,
                                 fields=['close', 'volume', 'high', 'low'])

            if len(stock_data) < 20:
                continue

            # 计算涨幅
            return_1d = (stock_data['close'].iloc[-1] / stock_data['close'].iloc[-2] - 1) * 100

            # 计算成交量放大倍数
            volume_ratio = stock_data['volume'].iloc[-1] / stock_data['volume'].rolling(5).mean().iloc[-1]

            # 计算5日均线
            ma5 = stock_data['close'].rolling(5).mean()

            # 计算DMI指标
            dmi_signals = calculate_dmi(stock_data)

            # 强势个股标准
            if (return_1d > 1.0 and  # 涨幅大于1%
                volume_ratio > 1.3 and  # 成交量放大30%以上
                stock_data['close'].iloc[-1] > ma5.iloc[-1] and  # 站稳5日均线
                ma5.iloc[-1] > ma5.iloc[-2] and  # 5日均线上行
                dmi_signals['pdi'] > dmi_signals['mdi']):  # DMI多头信号

                strong_stocks.append(stock)

        except Exception as e:
            continue

    return strong_stocks[:50]  # 限制数量

def identify_strong_stocks_market(context):
    """识别全市场强势个股"""
    # 获取全市场股票
    all_stocks = list(get_all_securities('stock', context.previous_date).index)

    # 基础过滤
    all_stocks = filter_stocks(context, all_stocks)

    strong_stocks = []

    for stock in all_stocks[:1000]:  # 限制扫描数量
        try:
            # 获取股票数据
            stock_data = get_price(stock, count=20, end_date=context.previous_date,
                                 fields=['close', 'volume'])

            if len(stock_data) < 20:
                continue

            # 计算涨幅
            return_5d = (stock_data['close'].iloc[-1] / stock_data['close'].iloc[-6] - 1) * 100

            # 成交量放大
            volume_ratio = stock_data['volume'].iloc[-1] / stock_data['volume'].rolling(10).mean().iloc[-1]

            # 全市场强势标准（更严格）
            if (return_5d > 10.0 and  # 5日涨幅大于10%
                volume_ratio > 2.0):  # 成交量放大100%以上

                strong_stocks.append(stock)

        except Exception as e:
            continue

    return strong_stocks[:30]  # 限制数量

def filter_stocks(context, stocks):
    """基础股票过滤"""
    filtered_stocks = []
    current_data = get_current_data()

    for stock in stocks:
        # 过滤条件
        if (stock in g.blacklist or  # 黑名单
            stock.startswith('688') or  # 科创板
            stock.startswith('300') or  # 创业板
            current_data[stock].paused or  # 停牌
            current_data[stock].is_st or  # ST股票
            current_data[stock].day_open == current_data[stock].high_limit or  # 涨停
            current_data[stock].day_open == current_data[stock].low_limit):  # 跌停
            continue

        filtered_stocks.append(stock)

    return filtered_stocks

def calculate_dmi(data):
    """计算DMI指标"""
    high = data['high'].values
    low = data['low'].values
    close = data['close'].values

    # 计算DMI
    pdi = talib.PLUS_DI(high, low, close, timeperiod=14)
    mdi = talib.MINUS_DI(high, low, close, timeperiod=14)
    adx = talib.ADX(high, low, close, timeperiod=14)

    return {
        'pdi': pdi[-1] if len(pdi) > 0 else 0,
        'mdi': mdi[-1] if len(mdi) > 0 else 0,
        'adx': adx[-1] if len(adx) > 0 else 0
    }

def check_order_status(context):
    """检查委托状态 - 3秒检查机制"""
    orders = get_open_orders()

    for stock, order_list in orders.items():
        for order in order_list:
            order_id = order.order_id

            # 检查订单是否超过3秒未成交
            if order_id in g.pending_orders:
                elapsed_time = (context.current_dt - g.pending_orders[order_id]['time']).total_seconds()

                if elapsed_time >= 3:  # 3秒未成交
                    # 撤单
                    cancel_order(order)
                    log.info(f"撤销超时订单: {stock}, 订单ID: {order_id}")

                    # 重新下单
                    retry_order(context, stock, order_id)

                    # 清理记录
                    del g.pending_orders[order_id]

def retry_order(context, stock, original_order_id):
    """重新下单"""
    if original_order_id not in g.order_retry_count:
        g.order_retry_count[original_order_id] = 0

    g.order_retry_count[original_order_id] += 1

    # 超过最大重试次数
    if g.order_retry_count[original_order_id] > g.max_retry:
        log.info(f"订单重试次数超限: {stock}")
        return

    current_data = get_current_data()
    current_price = current_data[stock].last_price

    # 获取原订单信息
    original_order = get_order(original_order_id)

    if original_order.side == 'long':  # 买入订单
        # 逐步提高买入价格
        price_adjustment = 1 + (g.order_retry_count[original_order_id] * 0.01)
        new_price = current_price * price_adjustment

        # 限制在涨停价以内
        limit_up_price = current_data[stock].high_limit
        new_price = min(new_price, limit_up_price * 0.99)

        # 重新买入
        new_order = order_target_value(stock, original_order.value, style=LimitOrderStyle(new_price))

    else:  # 卖出订单
        # 逐步降低卖出价格
        price_adjustment = 1 - (g.order_retry_count[original_order_id] * 0.01)
        new_price = current_price * price_adjustment

        # 限制在跌停价以上
        limit_down_price = current_data[stock].low_limit
        new_price = max(new_price, limit_down_price * 1.01)

        # 重新卖出
        new_order = order_target_value(stock, 0, style=LimitOrderStyle(new_price))

    # 记录新订单
    if new_order:
        g.pending_orders[new_order.order_id] = {
            'time': context.current_dt,
            'stock': stock,
            'retry_count': g.order_retry_count[original_order_id]
        }
        log.info(f"重新下单: {stock}, 第{g.order_retry_count[original_order_id]}次重试")

def smart_order(context, stock, target_value, order_type='buy'):
    """智能下单函数"""
    current_data = get_current_data()
    current_price = current_data[stock].last_price

    if order_type == 'buy':
        # 买入：以涨停价*0.99委托
        limit_price = current_data[stock].high_limit * 0.99
        order = order_target_value(stock, target_value, style=LimitOrderStyle(limit_price))
    else:
        # 卖出：以跌停价*1.01委托
        limit_price = current_data[stock].low_limit * 1.01
        order = order_target_value(stock, 0, style=LimitOrderStyle(limit_price))

    # 记录订单
    if order:
        g.pending_orders[order.order_id] = {
            'time': context.current_dt,
            'stock': stock,
            'retry_count': 0
        }
        log.info(f"智能下单: {stock}, 类型: {order_type}, 目标价值: {target_value}")

    return order

def check_positions(context):
    """检查持仓 - 动态止盈止损"""
    current_data = get_current_data()

    for stock in list(context.portfolio.positions.keys()):
        position = context.portfolio.positions[stock]

        if position.total_amount == 0:
            continue

        current_price = current_data[stock].last_price
        cost_price = position.avg_cost

        # 计算收益率
        return_pct = (current_price - cost_price) / cost_price

        # 获取股票技术指标
        stock_data = get_price(stock, count=20, end_date=context.previous_date,
                             fields=['close', 'high', 'low', 'volume'])

        if len(stock_data) < 20:
            continue

        dmi_signals = calculate_dmi(stock_data)

        # 动态止损条件
        should_stop_loss = False

        # 1. DMI转空头信号（第一优先级）
        if dmi_signals['pdi'] < dmi_signals['mdi']:
            should_stop_loss = True
            log.info(f"DMI转空头止损: {stock}")

        # 2. 固定止损
        elif return_pct < -g.stop_loss_pct:
            should_stop_loss = True
            log.info(f"固定止损: {stock}, 亏损: {return_pct:.2%}")

        # 3. 跌破5日均线且放量
        else:
            ma5 = stock_data['close'].rolling(5).mean().iloc[-1]
            volume_ratio = stock_data['volume'].iloc[-1] / stock_data['volume'].rolling(5).mean().iloc[-1]

            if current_price < ma5 and volume_ratio > 1.5:
                should_stop_loss = True
                log.info(f"破位止损: {stock}")

        # 执行止损
        if should_stop_loss:
            smart_order(context, stock, 0, 'sell')
            g.just_sold.append(stock)
            continue

        # 动态止盈条件
        should_take_profit = False

        # 1. DMI趋势减弱
        if dmi_signals['adx'] < 55 and return_pct > 0.05:
            should_take_profit = True
            log.info(f"DMI趋势减弱止盈: {stock}")

        # 2. 涨停打开立即卖出
        elif (current_data[stock].high_limit > 0 and
              current_price < current_data[stock].high_limit * 0.99):
            should_take_profit = True
            log.info(f"涨停打开止盈: {stock}")

        # 执行止盈
        if should_take_profit:
            smart_order(context, stock, 0, 'sell')
            g.just_sold.append(stock)

def check_limit_up(context):
    """检查涨停股票"""
    current_data = get_current_data()

    for stock in list(context.portfolio.positions.keys()):
        position = context.portfolio.positions[stock]

        if position.total_amount == 0:
            continue

        # 检查是否涨停
        if (current_data[stock].high_limit > 0 and
            current_data[stock].last_price >= current_data[stock].high_limit * 0.995):
            log.info(f"持仓涨停: {stock}")
            # 涨停继续持有，不卖出

def adjust_positions(context):
    """调仓函数 - 基于四层级板块选股"""
    # 获取候选股票池
    candidate_stocks = []

    # 优先从板块中的强势股3选择
    candidate_stocks.extend(g.strong_stocks_in_sector)

    # 补充从强势个股4选择
    candidate_stocks.extend(g.strong_stocks_market)

    # 去重并过滤
    candidate_stocks = list(set(candidate_stocks))
    candidate_stocks = filter_stocks(context, candidate_stocks)

    # 排除刚卖出的股票
    candidate_stocks = [s for s in candidate_stocks if s not in g.just_sold]

    # 按技术指标排序选股
    final_stocks = rank_stocks_by_signals(context, candidate_stocks)

    # 获取当前持仓
    current_positions = list(context.portfolio.positions.keys())
    current_positions = [s for s in current_positions if context.portfolio.positions[s].total_amount > 0]

    # 确定要卖出的股票
    stocks_to_sell = []
    for stock in current_positions:
        if stock not in final_stocks[:g.stock_num]:
            stocks_to_sell.append(stock)

    # 确定要买入的股票
    stocks_to_buy = []
    for stock in final_stocks[:g.stock_num]:
        if stock not in current_positions:
            stocks_to_buy.append(stock)

    # 执行卖出
    for stock in stocks_to_sell:
        smart_order(context, stock, 0, 'sell')
        log.info(f"调仓卖出: {stock}")

    # 执行买入
    if len(stocks_to_buy) > 0:
        # 计算可用资金
        available_cash = context.portfolio.available_cash * g.max_position
        target_value_per_stock = available_cash / len(stocks_to_buy)

        for stock in stocks_to_buy:
            smart_order(context, stock, target_value_per_stock, 'buy')
            log.info(f"调仓买入: {stock}, 目标金额: {target_value_per_stock}")

    # 清理刚卖出列表
    g.just_sold = []

def rank_stocks_by_signals(context, stocks):
    """根据技术信号对股票排序"""
    stock_scores = []

    for stock in stocks:
        try:
            # 获取股票数据
            stock_data = get_price(stock, count=30, end_date=context.previous_date,
                                 fields=['close', 'high', 'low', 'volume'])

            if len(stock_data) < 30:
                continue

            score = 0

            # DMI信号评分（最重要）
            dmi_signals = calculate_dmi(stock_data)
            if dmi_signals['pdi'] > dmi_signals['mdi']:
                score += 50
            if dmi_signals['adx'] > 55:
                score += 30

            # 均线信号评分
            ma5 = stock_data['close'].rolling(5).mean().iloc[-1]
            ma20 = stock_data['close'].rolling(20).mean().iloc[-1]
            current_price = stock_data['close'].iloc[-1]

            if current_price > ma5 > ma20:
                score += 20

            # 成交量信号评分
            volume_ratio = stock_data['volume'].iloc[-1] / stock_data['volume'].rolling(10).mean().iloc[-1]
            if volume_ratio > 1.5:
                score += 10

            # 涨幅评分
            return_5d = (current_price / stock_data['close'].iloc[-6] - 1) * 100
            if 0 < return_5d < 20:  # 适度涨幅
                score += 15

            stock_scores.append((stock, score))

        except Exception as e:
            continue

    # 按评分排序
    stock_scores.sort(key=lambda x: x[1], reverse=True)

    return [stock for stock, score in stock_scores]
    # 2. 基础过滤
    stocks = filter_kcbj_stock(stocks)  # 过滤科创北交
    stocks = filter_st_stock(stocks)  # 过滤ST股票
    stocks = filter_paused_stock(stocks)  # 过滤停牌股票
    stocks = filter_new_stock(context, stocks)  # 过滤次新股
    stocks = filter_limitup_stock(context, stocks)  # 过滤涨停股票
    stocks = filter_limitdown_stock(context, stocks)  # 过滤跌停股票
    stocks = filter_highprice_stock(context, stocks)  # 过滤高价股
    
    # 3. 基本面筛选（参考策略核心）
    stocks = get_peg_filter(context, stocks)
    
    # 4. 黑名单过滤
    recent_limit_up_list = get_recent_limit_up_stock(context, stocks, g.limit_days)
    black_list = list(set(g.not_buy_again_list).intersection(set(recent_limit_up_list)))
    target_list = [stock for stock in stocks if stock not in black_list]
    
    # 5. 限制数量
    g.choice = target_list[:g.stock_num]
    log.info(f'选股完成，共选出{len(g.choice)}只股票')

def get_peg_filter(context, stocks):
    """基本面筛选 - 基于参考策略"""
    q = query(
        valuation.code,
        valuation.pe_ratio,
        indicator.inc_net_profit_year_on_year,
        indicator.roe,
        indicator.roa,
        valuation.pb_ratio
    ).filter(
        indicator.roe > 0.15,  # ROE > 15%
        indicator.roa > 0.10,  # ROA > 10%
        valuation.code.in_(stocks)
    )
    
    df_fundamentals = get_fundamentals(q, date=None)
    if len(df_fundamentals) == 0:
        return []
    
    stocks = list(df_fundamentals.code)
    
    # 按市值排序，优先选择小市值
    df = get_fundamentals(query(valuation.code).filter(valuation.code.in_(stocks)).order_by(valuation.market_cap.asc()))
    return list(df.code)

def adjust_positions(context):
    """月度调仓"""
    if len(g.choice) == 0:
        return
    
    cdata = get_current_data()
    
    # 卖出不在选股池中的股票
    for stock in list(context.portfolio.positions.keys()):
        if stock not in g.choice and not cdata[stock].paused:
            log.info(f'卖出: {stock} {cdata[stock].name}')
            order_target(stock, 0)
            g.just_sold.append(stock)
            
            if len(g.just_sold) >= g.limit_days:
                g.just_sold = g.just_sold[-g.stock_num:]
    
    # 买入新股票
    position_count = len(context.portfolio.positions)
    if g.stock_num > position_count:
        psize = context.portfolio.available_cash / (g.stock_num - position_count)
        for stock in g.choice:
            if stock not in context.portfolio.positions:
                log.info(f'买入: {stock} {cdata[stock].name}')
                order_value(stock, psize)
                if len(context.portfolio.positions) == g.stock_num:
                    break

def prepare_high_limit_list(context):
    """准备昨日涨停股票列表"""
    g.high_limit_list = []
    hold_list = list(context.portfolio.positions.keys())
    
    if hold_list:
        df = get_price(hold_list, end_date=context.previous_date, frequency='daily',
                      fields=['close', 'high_limit'], count=1, panel=False)
        g.high_limit_list = df[df['close'] == df['high_limit']]['code'].tolist()
    
    # 更新黑名单
    g.hold_list = list(context.portfolio.positions.keys())
    g.history_hold_list.append(g.hold_list)
    
    if len(g.history_hold_list) >= g.limit_days:
        g.history_hold_list = g.history_hold_list[-g.limit_days:]
    
    temp_set = set()
    for hold_list in g.history_hold_list:
        for stock in hold_list:
            temp_set.add(stock)
    g.not_buy_again_list = list(temp_set)

def check_limit_up(context):
    """检查涨停股票"""
    if len(g.high_limit_list) == 0:
        return
    
    current_data = get_current_data()
    for stock in g.high_limit_list:
        # 涨停打开就卖出
        if current_data[stock].last_price < current_data[stock].high_limit:
            order_target(stock, 0)
            log.info(f'涨停打开，卖出: {stock}')
            g.just_sold.append(stock)
            
            if len(g.just_sold) >= g.limit_days:
                g.just_sold = g.just_sold[-g.stock_num:]
        else:
            log.info(f'涨停继续持有: {stock}')
    
    # 补充持仓
    position_count = len(context.portfolio.positions)
    if g.stock_num > position_count and position_count != 0:
        select_stocks(context)
        cdata = get_current_data()
        psize = context.portfolio.available_cash / (g.stock_num - position_count)
        for stock in g.choice:
            if stock not in context.portfolio.positions:
                order_value(stock, psize)
                if len(context.portfolio.positions) == g.stock_num:
                    break

def check_order_status(context):
    """检查订单状态 - 5分钟超时处理"""
    for order in get_open_orders():
        if order.created_at and (context.current_dt - order.created_at).total_seconds() > 300:  # 5分钟
            cancel_order(order)
            log.info(f'订单超时取消: {order.security}')
            
            # 重新委托
            if order.amount > 0:  # 买入订单
                order_value(order.security, order.amount * order.price)
            else:  # 卖出订单
                order_target_percent(order.security, 0)

# 过滤函数
def filter_kcbj_stock(stock_list):
    """过滤科创北交股票"""
    return [stock for stock in stock_list if not (stock[0] == '4' or stock[0] == '8' or stock[:2] == '68')]

def filter_st_stock(stock_list):
    """过滤ST股票"""
    current_data = get_current_data()
    return [stock for stock in stock_list
            if not current_data[stock].is_st
            and 'ST' not in current_data[stock].name
            and '*' not in current_data[stock].name
            and '退' not in current_data[stock].name]

def filter_paused_stock(stock_list):
    """过滤停牌股票"""
    current_data = get_current_data()
    return [stock for stock in stock_list if not current_data[stock].paused]

def filter_new_stock(context, stock_list):
    """过滤次新股"""
    yesterday = context.previous_date
    return [stock for stock in stock_list 
            if yesterday - get_security_info(stock).start_date >= datetime.timedelta(days=250)]

def filter_limitup_stock(context, stock_list):
    """过滤涨停股票"""
    last_prices = history(1, unit='1m', field='close', security_list=stock_list)
    current_data = get_current_data()
    return [stock for stock in stock_list 
            if stock in context.portfolio.positions.keys()
            or last_prices[stock][-1] < current_data[stock].high_limit * 0.97]

def filter_limitdown_stock(context, stock_list):
    """过滤跌停股票"""
    last_prices = history(1, unit='1m', field='close', security_list=stock_list)
    current_data = get_current_data()
    return [stock for stock in stock_list 
            if stock in context.portfolio.positions.keys()
            or last_prices[stock][-1] > current_data[stock].low_limit * 1.04]

def filter_highprice_stock(context, stock_list):
    """过滤高价股"""
    last_prices = history(1, unit='1m', field='close', security_list=stock_list)
    return [stock for stock in stock_list 
            if stock in context.portfolio.positions.keys()
            or last_prices[stock][-1] < 10]

def get_recent_limit_up_stock(context, stock_list, recent_days):
    """获取最近N个交易日内有涨停的股票"""
    stat_date = context.previous_date
    new_list = []
    for stock in stock_list:
        df = get_price(stock, end_date=stat_date, frequency='daily', 
                      fields=['close', 'high_limit'], count=recent_days, panel=False, fill_paused=False)
        df = df[df['close'] == df['high_limit']]
        if len(df) > 0:
            new_list.append(stock)
    return new_list

def after_trading_end(context):
    """收盘后处理"""
    # 更新最大净值
    current_value = context.portfolio.total_value
    if current_value > context.max_portfolio_value:
        context.max_portfolio_value = current_value
    
    # 计算日收益率
    if context.last_portfolio_value > 0:
        daily_return = (current_value - context.last_portfolio_value) / context.last_portfolio_value
        log.info(f'日收益率: {daily_return:.4f}')
    
    context.last_portfolio_value = current_value 