# 聚宽量化策略 - 基于改进后的设计文档
# 融合郭海培老师交易体系和参考策略成功要素

import pandas as pd
import numpy as np
from jqdata import *
import datetime

def initialize(context):
    """初始化函数 - 基于改进后的设计文档"""
    # 设置基准
    set_benchmark('000300.XSHG')
    # 设置滑点
    set_slippage(FixedSlippage(0.02))
    # 设置交易成本
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, open_commission=0.0003, close_commission=0.0003, close_today_commission=0, min_commission=5), type='fund')
    # 开启动态复权模式
    set_option('use_real_price', True)
    # 设置避免未来数据
    set_option('avoid_future_data', True)
    
    # 策略参数 - 基于参考策略成功要素
    g.stock_num = 10  # 持股数量
    g.limit_days = 30  # 黑名单限制天数
    g.high_limit_list = []  # 昨日涨停股票列表
    g.just_sold = []  # 刚卖出的股票
    g.not_buy_again_list = []  # 黑名单股票
    g.history_hold_list = []  # 历史持仓记录
    
    # 初始化变量
    context.max_portfolio_value = context.portfolio.total_value
    context.last_portfolio_value = context.portfolio.total_value
    
    # 定时任务
    run_daily(prepare_high_limit_list, time='9:05')
    run_daily(check_limit_up, time='14:00')
    run_monthly(select_stocks, -1, time='9:30', force=True)
    run_monthly(adjust_positions, -1, time='14:55', force=True)
    
    # 订单状态检查（每5分钟）
    for i in range(9, 15):
        for j in range(0, 60, 5):
            time_str = f"{i:02d}:{j:02d}"
            run_daily(check_order_status, time=time_str)

def select_stocks(context):
    """月度选股 - 基于参考策略的成功要素"""
    # 1. 获取所有股票
    stocks = get_all_securities('stock', context.previous_date).index.tolist()
    
    # 2. 基础过滤
    stocks = filter_kcbj_stock(stocks)  # 过滤科创北交
    stocks = filter_st_stock(stocks)  # 过滤ST股票
    stocks = filter_paused_stock(stocks)  # 过滤停牌股票
    stocks = filter_new_stock(context, stocks)  # 过滤次新股
    stocks = filter_limitup_stock(context, stocks)  # 过滤涨停股票
    stocks = filter_limitdown_stock(context, stocks)  # 过滤跌停股票
    stocks = filter_highprice_stock(context, stocks)  # 过滤高价股
    
    # 3. 基本面筛选（参考策略核心）
    stocks = get_peg_filter(context, stocks)
    
    # 4. 黑名单过滤
    recent_limit_up_list = get_recent_limit_up_stock(context, stocks, g.limit_days)
    black_list = list(set(g.not_buy_again_list).intersection(set(recent_limit_up_list)))
    target_list = [stock for stock in stocks if stock not in black_list]
    
    # 5. 限制数量
    g.choice = target_list[:g.stock_num]
    log.info(f'选股完成，共选出{len(g.choice)}只股票')

def get_peg_filter(context, stocks):
    """基本面筛选 - 基于参考策略"""
    q = query(
        valuation.code,
        valuation.pe_ratio,
        indicator.inc_net_profit_year_on_year,
        indicator.roe,
        indicator.roa,
        valuation.pb_ratio
    ).filter(
        indicator.roe > 0.15,  # ROE > 15%
        indicator.roa > 0.10,  # ROA > 10%
        valuation.code.in_(stocks)
    )
    
    df_fundamentals = get_fundamentals(q, date=None)
    if len(df_fundamentals) == 0:
        return []
    
    stocks = list(df_fundamentals.code)
    
    # 按市值排序，优先选择小市值
    df = get_fundamentals(query(valuation.code).filter(valuation.code.in_(stocks)).order_by(valuation.market_cap.asc()))
    return list(df.code)

def adjust_positions(context):
    """月度调仓"""
    if len(g.choice) == 0:
        return
    
    cdata = get_current_data()
    
    # 卖出不在选股池中的股票
    for stock in list(context.portfolio.positions.keys()):
        if stock not in g.choice and not cdata[stock].paused:
            log.info(f'卖出: {stock} {cdata[stock].name}')
            order_target(stock, 0)
            g.just_sold.append(stock)
            
            if len(g.just_sold) >= g.limit_days:
                g.just_sold = g.just_sold[-g.stock_num:]
    
    # 买入新股票
    position_count = len(context.portfolio.positions)
    if g.stock_num > position_count:
        psize = context.portfolio.available_cash / (g.stock_num - position_count)
        for stock in g.choice:
            if stock not in context.portfolio.positions:
                log.info(f'买入: {stock} {cdata[stock].name}')
                order_value(stock, psize)
                if len(context.portfolio.positions) == g.stock_num:
                    break

def prepare_high_limit_list(context):
    """准备昨日涨停股票列表"""
    g.high_limit_list = []
    hold_list = list(context.portfolio.positions.keys())
    
    if hold_list:
        df = get_price(hold_list, end_date=context.previous_date, frequency='daily',
                      fields=['close', 'high_limit'], count=1, panel=False)
        g.high_limit_list = df[df['close'] == df['high_limit']]['code'].tolist()
    
    # 更新黑名单
    g.hold_list = list(context.portfolio.positions.keys())
    g.history_hold_list.append(g.hold_list)
    
    if len(g.history_hold_list) >= g.limit_days:
        g.history_hold_list = g.history_hold_list[-g.limit_days:]
    
    temp_set = set()
    for hold_list in g.history_hold_list:
        for stock in hold_list:
            temp_set.add(stock)
    g.not_buy_again_list = list(temp_set)

def check_limit_up(context):
    """检查涨停股票"""
    if len(g.high_limit_list) == 0:
        return
    
    current_data = get_current_data()
    for stock in g.high_limit_list:
        # 涨停打开就卖出
        if current_data[stock].last_price < current_data[stock].high_limit:
            order_target(stock, 0)
            log.info(f'涨停打开，卖出: {stock}')
            g.just_sold.append(stock)
            
            if len(g.just_sold) >= g.limit_days:
                g.just_sold = g.just_sold[-g.stock_num:]
        else:
            log.info(f'涨停继续持有: {stock}')
    
    # 补充持仓
    position_count = len(context.portfolio.positions)
    if g.stock_num > position_count and position_count != 0:
        select_stocks(context)
        cdata = get_current_data()
        psize = context.portfolio.available_cash / (g.stock_num - position_count)
        for stock in g.choice:
            if stock not in context.portfolio.positions:
                order_value(stock, psize)
                if len(context.portfolio.positions) == g.stock_num:
                    break

def check_order_status(context):
    """检查订单状态 - 5分钟超时处理"""
    for order in get_open_orders():
        if order.created_at and (context.current_dt - order.created_at).total_seconds() > 300:  # 5分钟
            cancel_order(order)
            log.info(f'订单超时取消: {order.security}')
            
            # 重新委托
            if order.amount > 0:  # 买入订单
                order_value(order.security, order.amount * order.price)
            else:  # 卖出订单
                order_target_percent(order.security, 0)

# 过滤函数
def filter_kcbj_stock(stock_list):
    """过滤科创北交股票"""
    return [stock for stock in stock_list if not (stock[0] == '4' or stock[0] == '8' or stock[:2] == '68')]

def filter_st_stock(stock_list):
    """过滤ST股票"""
    current_data = get_current_data()
    return [stock for stock in stock_list
            if not current_data[stock].is_st
            and 'ST' not in current_data[stock].name
            and '*' not in current_data[stock].name
            and '退' not in current_data[stock].name]

def filter_paused_stock(stock_list):
    """过滤停牌股票"""
    current_data = get_current_data()
    return [stock for stock in stock_list if not current_data[stock].paused]

def filter_new_stock(context, stock_list):
    """过滤次新股"""
    yesterday = context.previous_date
    return [stock for stock in stock_list 
            if yesterday - get_security_info(stock).start_date >= datetime.timedelta(days=250)]

def filter_limitup_stock(context, stock_list):
    """过滤涨停股票"""
    last_prices = history(1, unit='1m', field='close', security_list=stock_list)
    current_data = get_current_data()
    return [stock for stock in stock_list 
            if stock in context.portfolio.positions.keys()
            or last_prices[stock][-1] < current_data[stock].high_limit * 0.97]

def filter_limitdown_stock(context, stock_list):
    """过滤跌停股票"""
    last_prices = history(1, unit='1m', field='close', security_list=stock_list)
    current_data = get_current_data()
    return [stock for stock in stock_list 
            if stock in context.portfolio.positions.keys()
            or last_prices[stock][-1] > current_data[stock].low_limit * 1.04]

def filter_highprice_stock(context, stock_list):
    """过滤高价股"""
    last_prices = history(1, unit='1m', field='close', security_list=stock_list)
    return [stock for stock in stock_list 
            if stock in context.portfolio.positions.keys()
            or last_prices[stock][-1] < 10]

def get_recent_limit_up_stock(context, stock_list, recent_days):
    """获取最近N个交易日内有涨停的股票"""
    stat_date = context.previous_date
    new_list = []
    for stock in stock_list:
        df = get_price(stock, end_date=stat_date, frequency='daily', 
                      fields=['close', 'high_limit'], count=recent_days, panel=False, fill_paused=False)
        df = df[df['close'] == df['high_limit']]
        if len(df) > 0:
            new_list.append(stock)
    return new_list

def after_trading_end(context):
    """收盘后处理"""
    # 更新最大净值
    current_value = context.portfolio.total_value
    if current_value > context.max_portfolio_value:
        context.max_portfolio_value = current_value
    
    # 计算日收益率
    if context.last_portfolio_value > 0:
        daily_return = (current_value - context.last_portfolio_value) / context.last_portfolio_value
        log.info(f'日收益率: {daily_return:.4f}')
    
    context.last_portfolio_value = current_value 