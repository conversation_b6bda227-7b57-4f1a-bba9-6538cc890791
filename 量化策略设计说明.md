# 量化策略设计说明

**最后更新时间**: 2025-07-31
**版本**: v2.0
**状态**: 已验证编辑功能正常

---

一、策略设计总纲

本策略以"先大盘、再板块、再个股"的多层级决策为核心，结合多因子、技术面、量价结构、均线角度、波段分析等思想，动态识别市场状态（牛市/熊市/震荡市），并根据不同市场状态切换最优选股、仓位、风控和动态止盈止损方案。目标是实现高收益、低回撤、强鲁棒性，具备实盘可用性和可持续升级能力。核心技术指标DMI作为最可靠的趋势判断工具，不存在背离现象，为策略提供最稳定的交易信号支持。

**核心思想来源**：基于郭海培老师《王者之声》和《胜者为王》的实战交易体系，融合均线扣抵法、量价结构、角度分析、DMI/KD指标等核心技术。

---

二、市场状态判定（牛/熊/震荡市）

A、判定方法
A1、**核心指标：60日均线（MA60）**
  - 收盘价在MA60上方，且MA60上行，均线多头排列：牛市
  - 收盘价在MA60下方，且MA60下行，均线空头排列：熊市
  - MA60走平，价格围绕其反复穿越，短中长期均线黏合收敛：震荡市
A2、**辅助指标**：
  - 均线角度（扣抵法）、量能结构、DMI、KD等

B、市场风险判断与仓位控制 - **核心风控机制**
B1、**核心市场指数监控**：
  - **沪深300指数（000300）**：作为A股市场整体风险判断的主要指标
  - **富时A50期指连续CN0Y**：作为市场情绪和外资流向的重要参考指标

B2、**市场风险判断机制**：
a1、**沪深300风险判断**：
   - 指数跌破60日均线且MA60下行：高风险，仓位控制在0-20%
   - 指数在60日均线附近震荡：中等风险，仓位控制在30-60%
   - 指数站稳60日均线且MA60上行：低风险，仓位可提升至60-100%

a2、**富时A50期指风险判断**：
   - 期指连续下跌且成交量放大：市场恐慌情绪上升，减仓操作
   - 期指企稳反弹且成交量配合：市场情绪好转，可适当加仓
   - 期指与现货指数背离：关注外资流向变化，调整策略

B3、**动态仓位控制策略**：
- **高风险期**：沪深300跌破关键支撑 + A50期指下跌 = 仓位0-20%
- **中风险期**：沪深300震荡整理 + A50期指企稳 = 仓位30-60%
- **低风险期**：沪深300突破上行 + A50期指配合 = 仓位60-100%

B4、**重要说明**：市场风险判断是整个策略的基础，通过沪深300和富时A50期指的双重确认，确保在不同市场环境下采用最适合的仓位策略，这是实现长期稳定收益的关键。

C、震荡市判定补充
- MA60水平走平，价格反复穿越，且5/10/20/60日均线黏合，量能萎缩或无明显趋势

D、牛熊市详细判定标准
D1、**牛市特征**：
- 1年线金叉2年线，10年线上升
- 均线多头排列：5日>10日>20日>60日>120日>240日
- DMI指标：PDI>MDI，ADX>55且向上
- 量价结构：价涨量增角度陡，价跌量缩角度缓
- **沪深300站稳60日均线且上行**
- **富时A50期指趋势向上**

D2、**熊市特征**：
- 1年线死叉2年线
- 均线空头排列
- DMI指标：MDI>PDI，ADX>55且向上
- 量价结构：价跌量增角度陡，价涨量缩角度缓
- **沪深300跌破60日均线且下行**
- **富时A50期指趋势向下**

D3、**黄金坑特征**：
- 月K线：10年线、5年线、2年线、1年线向上
- 或10年线、5年线向上，2年线、1年线向下但1年线金叉2年线向上

D4、**震荡市特征**：
- 60日均线水平走平，价格围绕其反复穿越
- 短中长期均线黏合收敛
- 量能萎缩或无明显趋势
- DMI指标：ADX在PDI、MDI下方波动，无明确趋势
- **沪深300在60日均线附近震荡**
- **富时A50期指无明显趋势**

---

三、多层级决策流程（大盘→板块→个股）

A、大盘层
- 判断市场状态（牛/熊/震荡）
- 决定整体仓位上限、风控参数、策略切换
- **监控沪深300和富时A50期指风险**

B、板块层
- 行业/概念/主题轮动，优选强势板块
- 量价、均线、DMI、资金流等多因子打分
- 剔除弱势、无题材、无量、黑名单板块

B1、**强势板块动态识别机制** - **核心选股基础**：
a1、**每日强势板块识别标准**：
   - 板块指数涨幅排名前20%
   - 板块内个股涨停数量≥3只
   - 板块成交量较前5日平均放大50%以上
   - 板块指数站稳5日均线且5日均线上行
   - 板块资金净流入为正且持续3天以上

a2、**强势板块自定义板块管理** - **每日更新机制**：
   - **创建"强势板块"自定义板块**：每天收盘后自动更新
   - **每日更新流程**：
     - 扫描所有板块，按强势标准筛选
     - 将符合条件的板块加入"强势板块"
     - 剔除不再符合强势标准的板块
     - 保持板块池的动态更新和优胜劣汰

a3、**板块强势持续性判断**：
   - **继续持有条件**：板块仍符合强势标准
   - **剔除条件**：连续3天不符合强势标准
   - **重新纳入条件**：重新符合强势标准且持续2天以上

---

四、DMI趋势判断系统（**最重要核心指标**）

A、DMI指标优势 - **策略核心依据**
**重要说明**：DMI指标是本策略中**最重要的核心指标**，是**最老实可靠的趋势指标**，**不存在任何背离现象**，信号最为明确可靠。在所有技术指标中，DMI应作为**第一优先级**的决策依据。

A1、**核心优势**：
- **最老实可靠的趋势指标**：**不存在背离现象**，信号明确，是最可信赖的指标
- **趋势强弱判断准确**：通过ADX白线判断趋势强度，精确度极高
- **多空转换信号清晰**：PDI（红线）、MDI（黄线）交叉提供明确的买卖信号
- **无欺骗性信号**：与KD等指标不同，DMI不会产生虚假信号

B、DMI信号系统
B1、**多头信号**：
   - PDI（红线）在上，MDI（黄线）在下
   - ADX（白线）向上且大于55，趋势最强
   - 开口越大，多头趋势越强

B2、**空头信号**：
   - MDI（黄线）在上，PDI（红线）在下
   - ADX（白线）向上且大于55，趋势最强
   - 开口越大，空头趋势越强

B3、**趋势转换信号**：
   - 空头结束：黄线跌破白线，且白线向下
   - 多头启动：红线上穿黄线，且白线开始向上

B4、**无趋势判断**：
   - ADX（白线）在PDI和MDI下方波动
   - PDI和MDI交织缠绕
   - 此时不适合操作

C、DMI策略应用
1. **趋势确认**：
   - ADX>55且向上：强势趋势确立
   - 下跌时找买点，不找卖点
   - 当ADX触及55后转向：趋势减弱信号

2. **趋势强度分级**：
   - ADX>55：超强趋势
   - ADX 30-55：中等趋势
   - ADX<30：弱趋势或盘整

---

五、动态止盈止损系统 - **收益最大化核心**

A、动态止盈策略 - **收益最大化关键**
**核心原则**：动态止盈是实现收益最大化的关键，不采用固定止盈点，而是根据趋势强弱和技术指标动态调整。

1. **DMI趋势判断止盈**（**第一优先级**）：
   - **多头强势持有**：PDI>MDI且ADX>55，坚决不止盈，让利润奔跑
   - **趋势减弱预警**：ADX转向下行，设置移动止盈线
   - **趋势转弱止盈**：PDI跌破MDI，立即止盈

2. **KD指标辅助止盈**（**第三优先级**）：
   - **重要提醒**：KD指标存在背离现象，**不能单纯以过热而卖出**
   - **正确使用方法**：
     - KD顶背离**一次可忽略**，**两次提高警觉**
     - **放量可化解背离**，继续持有
     - 只有当KD过热**且K线走平**、**量能萎缩**时才考虑止盈
     - **关键判断**：只要K线和成交量能继续跟上，即使KD过热也要持有

3. **均K线和均量线止盈**（**第二优先级**）：
   - **均K线（价格均线）止盈信号**：
     - 跌破5日均线：考虑部分止盈
     - 跌破10日均线：考虑大部分止盈
     - 跌破20日均线：全部止盈
   - **均量线（成交量均线）止盈信号**：
     - 均量线开始缩量反转向下：预警信号
     - 成交量跌破5日均量线：考虑止盈
     - 成交量持续萎缩：准备止盈

4. **移动止盈线设置**：
   - **强势股**（DMI强势）：移动止盈设为20日均线
   - **中势股**（DMI中等）：移动止盈设为10日均线
   - **弱势股**（DMI转弱）：移动止盈设为5日均线

B、动态止损策略 - **风险最小化核心**
**核心原则**：动态止损是控制风险的关键，需要快速响应市场变化，严格执行止损纪律。

1. **DMI趋势止损**（**第一优先级**）：
   - **趋势转空止损**：PDI跌破MDI，立即止损，不可犹豫
   - **趋势减弱止损**：ADX跌破ADXR，预警止损
   - **无趋势止损**：ADX在PDI、MDI下方，市场无趋势，止损观望

2. **均K线和均量线止损**（**第二优先级**）：
   - **破位止损**：跌破5日均线且成交量放大，果断止损
   - **趋势止损**：跌破10日均线且均线开始下行，及时止损
   - **量能止损**：成交量跌破5日均量线且持续萎缩，预警止损

3. **市场风险止损**（**系统性风险**）：
   - **大盘风险止损**：沪深300跌破60日均线，全面减仓
   - **A50期指止损**：富时A50期指连续下跌且放量，及时止损
   - **系统性风险**：两大指数同时转弱，立即清仓

4. **移动止损线设置**：
   - **强势股**（DMI强势）：移动止损设为20日均线
   - **中势股**（DMI中等）：移动止损设为10日均线
   - **弱势股**（DMI转弱）：移动止损设为5日均线

5. **特殊情况止损**：
   - **涨停打开止损**：涨停板打开立即卖出，避免大幅回撤
   - **黑天鹅事件**：突发重大利空，不计成本立即止损
   - **技术破位**：重要技术位破位且放量，果断止损

C、动态调仓机制 - **灵活应变核心**
**核心原则**：不固定调仓周期，根据个股和板块强弱变化进行动态调仓，强者恒强，弱者淘汰。

1. **调仓频率策略**：
   - **不固定月度调仓**：改为完全动态调仓机制
   - **调仓周期灵活**：可以是日度、周度，甚至盘中调仓
   - **强弱决定调仓**：只要个股或板块不够强，立即调仓
   - **强势继续持有**：只要个股或板块依然很强，坚决持有

2. **调仓触发条件**：
   - **DMI转弱信号**：PDI跌破MDI，立即调仓
   - **均线破位信号**：跌破关键均线且成交量配合
   - **板块轮动信号**：板块失去强势地位，轮动到新强势板块
   - **个股相对弱势**：个股表现弱于板块平均水平
   - **KD背离确认**：KD顶背离两次且K线走平、量能萎缩

3. **持仓动态优化**：
   - **强势股策略**：继续持有，不轻易调仓
   - **转弱股策略**：立即减仓或清仓，换入更强个股
   - **新强势股**：及时调入符合强势标准的新个股
   - **板块轮动**：及时调入新的强势板块个股

4. **调仓执行原则**：
   - **快进快出**：发现强势立即买入，发现转弱立即卖出
   - **不恋战**：不对任何个股产生感情，严格按信号执行
   - **资金利用最大化**：确保资金始终投向最强势的标的

3. **板块强度评估指标**：
   - 板块指数相对强度（RSI）
   - 板块内个股上涨比例
   - 板块资金流入情况
   - 板块技术形态（突破、支撑、阻力）

### 3.3 个股层
- 多因子选股（基本面+技术面+资金面）
- 过滤ST、退市、停牌、黑名单个股
- 量价结构、均线排列、角度、波段、DMI/KD等综合打分
- 动态止盈止损、仓位分配

**强势个股动态识别机制** - **精选个股核心**：
1. **每日强势个股识别标准**：
   - **从强势板块中筛选**：只从"强势板块"中选择个股
   - **涨幅排名**：个股涨幅排名板块内前30%
   - **成交量放大**：个股成交量较前5日平均放大30%以上
   - **均线支撑**：个股站稳5日均线且5日均线上行
   - **技术形态**：个股技术形态良好（突破、金叉、DMI多头等）
   - **相对强度**：个股相对强度指标优于市场平均水平

2. **强势个股自定义板块管理** - **每日动态更新**：
   - **创建"强势个股"自定义板块**：每天收盘后自动更新
   - **每日更新流程**：
     - 从"强势板块"中扫描所有个股
     - 按强势标准筛选符合条件的个股
     - 将符合条件的个股加入"强势个股"板块
     - 剔除不再符合强势标准的个股
     - 保持个股池的动态更新和优胜劣汰

3. **个股强势持续性判断**：
   - **继续持有条件**：个股仍符合强势标准且所在板块仍强势
   - **剔除条件**：连续2天不符合强势标准或所在板块转弱
   - **重新纳入条件**：重新符合强势标准且持续1天以上

3. **个股强度评估指标**：
   - 个股相对强度（RSI）
   - 个股资金流入情况
   - 个股技术形态评分
   - 个股基本面评分

**板块个股联动机制**：
- 强势板块中的强势个股优先选择
- 板块轮动时及时调整个股持仓
- 板块走弱时及时减仓相关个股

---

## 4. 主要用到的技术指标与源码

### 4.1 KD指标（随机指标）
**重要说明**：KD指标存在背离现象，**不能单纯以过热而卖出股票**。在策略设计中，KD指标主要用于辅助判断，**真正的买卖决策应该以均K线（价格均线）和均量线（成交量均线）为主要依据**。

**参数设置**：
- **N**：最小值2.00，最大值100.00，默认值9.00（用于计算最高价和最低价的周期）
- **M1**：最小值2.00，最大值100.00，默认值3.00（K线的平滑周期）
- **M2**：最小值2.00，最大值100.00，默认值3.00（D线的平滑周期）

**完整源码（通达信副图公式）**：
```
X_1:=(CLOSE-LLV(LOW,N))/(HHV(HIGH,N)-LLV(LOW,N))*100;
K:SMA(X_1,M1,1);
D:SMA(K,M2,1);
KD开口:IF(K-D!=0,K-D,0),NODRAW,COLORRED;
月乖离:(CLOSE-MA(CLOSE,20))/MA(CLOSE,20)*100,NODRAW,COLORWHITE;
季乖离:(CLOSE-MA(CLOSE,60))/MA(CLOSE,60)*100,NODRAW,COLORWHITE;
量比:VOL/REF(MA(VOL,5),1),NODRAW,COLORYELLOW;
换手:VOL/CAPITAL*100,NODRAW,COLORLIRED;
金叉天数:BARSLAST(D>K),NODRAW,COLORRED;
死叉天数:BARSLAST(D<K),NODRAW,COLORYELLOW;
DRAWICON(CROSS(K,D),D,1);
DRAWICON(CROSS(D,K),D,2);
50;
80;
20;
```

**源码说明**：
- `X_1`：计算原始随机值（RSV）
- `K`：K线，对X_1进行M1周期简单移动平均
- `D`：D线，对K线进行M2周期简单移动平均
- `KD开口`：K与D的差值，红色，不显示
- `月乖离`：收盘价相对20日均线的乖离率，白色，不显示
- `季乖离`：收盘价相对60日均线的乖离率，白色，不显示
- `量比`：当前成交量与5日均量的比值，黄色，不显示
- `换手`：换手率，浅红色，不显示
- `金叉天数`：距离上次金叉的天数，红色，不显示
- `死叉天数`：距离上次死叉的天数，黄色，不显示
- `DRAWICON`：绘制金叉/死叉图标
- `50,80,20`：固定数值，代表50、80、20三个重要水平

**实战应用**：
- **过热区**：K和D值在80上方，需提防拉回和震荡
- **钝化**：指标创高后持续在高位，量能增温可维持钝化
- **背离**：一次背离可忽略，两次背离提高警觉，放量可化解背离
- **乖离率**：月乖离7-10%（大盘股）、10-15%（中小盘股）、15-20%（妖股）
- **金叉死叉**：K上穿D为金叉（买点），K下穿D为死叉（卖点）
- **开口大小**：KD开口越大，趋势越强

**均K线和均量线作为主要决策依据**：
- **均K线（价格均线）**：
  - 5日均线：短期趋势判断，跌破5日线考虑减仓
  - 10日均线：中期趋势判断，跌破10日线考虑止损
  - 20日均线：中期支撑，跌破20日线考虑清仓
  - 60日均线：长期趋势判断，站稳60日线考虑加仓

- **均量线（成交量均线）**：
  - 5日均量线：短期量能判断，放量突破5日均量线为买点
  - 10日均量线：中期量能判断，缩量跌破10日均量线为卖点
  - 均量线反转：均量线开始缩量反转向下，预示均线可能反转

**买卖决策优先级** - **核心决策体系**：
1. **第一优先级**：**DMI指标趋势判断**（最重要，无背离，最可靠）
2. **第二优先级**：**均K线趋势和均量线配合**（主要决策依据）
3. **第三优先级**：**KD指标辅助判断**（注意背离风险，不能单纯以过热卖出）

**重要提醒**：DMI指标作为最老实、最可靠的指标，应该是买卖决策的第一优先级。均K线和均量线是主要决策依据，KD指标只能作为辅助，且要特别注意背离风险。

**KD指标过热详细解读**：
- **上涨看D值，下跌看K值**
- **K和D的数值在80上方就是过热区**
- **月线级别KD值来到80以上**：
  - 通常是波段过热，先观察钝化的可能性
  - 当D值来到80时，行情由急涨转为缓涨模式
  - 高档月KD死叉有没有破80，先看长短均线的趋势，再看量能是不是增温，最后观察是否有背离
- **周线级别KD值来到80**：
  - 当周KD值出现死叉，不一定会马上反转向下
  - 大波段上涨之后，周KD死叉，反转向下的机率比较高
- **日周月KD值同时来到80上方**：
  - 短线高点最多在一两周之后
  - 中长线高点可能会在1-2个月甚至1个季节之后

**KD指标背离操作**：
- **背离一次**：可以忽略
- **背离两次**：提高警觉
- **放量可以化解背离**
- **顶背离修复**：到指标的起涨点（或者股价的起涨点）再涨一波
- **底背离修复**：到指标的起跌点（或者股价的起跌点）再跌一波

### 4.2 DMI指标（趋向指标）- **核心重点指标**
**重要说明**：DMI指标是量化策略中**最老实的指标**，**没有任何背离**，是最可靠的趋势判断工具。在策略设计中，DMI指标应作为核心趋势判断依据，优先于其他技术指标。

**参数设置**：
- **N**：最小值2.00，最大值90.00，默认值14.00（用于计算真实波幅和方向移动的周期）
- **M**：最小值2.00，最大值60.00，默认值6.00（用于计算ADX的平滑周期）

**完整源码（通达信副图公式）**：
```
MTR:=SUM(MAX(MAX(HIGH-LOW,ABS(HIGH-REF(CLOSE,1))),ABS(REF(CLOSE,1)-LOW)),N);
HD:=HIGH-REF(HIGH,1);
LD:=REF(LOW,1)-LOW;
DMP:=SUM(IF(HD>0&&HD>LD,HD,0),N);
DMM:=SUM(IF(LD>0&&LD>HD,LD,0),N);
PDI:DMP*100/MTR,COLOR0000FF,LINETHICK3;
MDI:DMM*100/MTR,COLORYELLOW,LINETHICK3;
ADX:MA(ABS(MDI-PDI)/(MDI+PDI)*100,M),COLORFFFFFF;
ADXR:(ADX+REF(ADX,M))/2,COLOR00FF00;
强:55.00,COLOR0000FF;
中:30.00,COLORFFFF00;
弱:20.00;
```

**源码说明**：
- `MTR`：真实波幅（True Range），取最高价-最低价、最高价-前收盘价、前收盘价-最低价的最大值
- `HD`：向上方向移动（High Direction）
- `LD`：向下方向移动（Low Direction）
- `DMP`：正向方向移动（Positive Directional Movement）
- `DMM`：负向方向移动（Negative Directional Movement）
- `PDI`：正向方向指标（+DI），蓝色，线宽3
- `MDI`：负向方向指标（-DI），黄色，线宽3
- `ADX`：平均方向指数（Average Directional Index），白色
- `ADXR`：平均方向指数平滑值，绿色
- `强:55.00`：强趋势线，蓝色
- `中:30.00`：中等趋势线，黄色
- `弱:20.00`：弱趋势线

**实战应用**：
- **无趋势**：ADX在PDI、MDI下方波动
- **多头**：PDI>MDI，ADX>PDI且向上
- **空头**：MDI>PDI，ADX>MDI且向上
- **强弱判断**：ADX>55极强，>75超强，>95极限值
- **转折信号**：ADX跌破ADXR为预警，ADX反转确认转折
- **趋势确认**：PDI上穿MDI为多头趋势，MDI上穿PDI为空头趋势
- **趋势强度**：ADX>30表示有趋势，>55表示强趋势

**DMI多空判别详细规则**：
- **多头确认**：PDI红线向上，ADX白线也向上到达55，说明趋势偏强，下跌是找买点
- **空头开始信号**：黄线向上红线向下，ADX白线大于黄线向上，空方确认
- **空头结束信号**：黄线向上红线向下，ADX白线拐头向下打到红线位置，空方结束确认
- **无趋势状态**：ADX在PDI、MDI下方，PDI和MDI纠缠，代表无趋势状态，不适合操作
- **末升段警讯**：ADX达到55位置不能再继续向上走，说明不强了，考虑找卖点，绝对不能追买入

### 4.3 ENE指标（轨道线）
**参数设置**：
- **N**：最小值2.00，最大值120.00，默认值25.00（用于计算移动平均线的周期）
- **M1**：最小值2.00，最大值120.00，默认值6.00（上轨偏离百分比）
- **M2**：最小值2.00，最大值120.00，默认值6.00（下轨偏离百分比）

**完整源码（通达信副图公式）**：
```
UPPER:(1+M1/100)*MA(CLOSE,N);
LOWER:(1-M2/100)*MA(CLOSE,N);
ENE:(UPPER+LOWER)/2;
```

**源码说明**：
- `UPPER`：上轨线，等于N日移动平均线乘以(1+M1/100)
- `LOWER`：下轨线，等于N日移动平均线乘以(1-M2/100)
- `ENE`：中轨线，等于上轨和下轨的平均值

**实战应用**：
- **下轨向上**：可买进
- **下轨向下**：需看月乖离，乖离够大才可买进
- **上轨**：头部确立信号
- **价格位置**：
  - 价格在上轨附近：超买区域，注意回调
  - 价格在下轨附近：超卖区域，注意反弹
  - 价格在中轨附近：正常波动区域
- **轨道宽度**：轨道越宽，波动越大；轨道越窄，波动越小

---

## 5. 量价、均线、角度、波段等核心思想

### 5.1 均线扣抵法（预判趋势的数学钥匙）
**核心原理**：
- 均线扣抵价远低于目前价位，均线上升角度越陡，助涨力道较强
- 均线扣抵价远高于目前价位，均线下降角度越陡，助跌力道越强
- 上涨趋势里均线扣抵价逐渐扣高，而目前价位横盘未涨或下跌，则上涨趋势将走平或反转
- 下跌趋势里均线扣抵价逐渐扣低，而目前价位横盘或上涨，则下跌趋势将走平或反转

**实战应用**：
- 计算次日"防守价"：若当前股价在MA5均线上方运行，可设定D4价格为关键防守位
- 识别支撑与压力区域：扣抵日价格处于低位区域，后续均线易上行形成支撑

### 5.2 量价结构分析
**多头量价结构**：
- 价涨量增角度陡→攻击波（买点）
- 价跌量缩角度缓→防守波（持股）

**空头量价结构**：
- 价跌量增角度陡→下跌波（卖点）
- 价涨量缩角度缓→反弹波（减仓）

**关键量能信号**：
- **表态量**：波段起涨、起跌前的转折讯号
- **启动量**：波段起涨起跌时突破型态，趋势的带量K线
- **凹洞量**：波段瞬间反转的多空转折讯号
- **凸量**：通常适用于熊市，为中长均线趋势向下时的多头陷阱

### 5.3 角度分析（同向波与反向波）
**同向波比强弱**：
- 本波上涨波与前波上涨波比较，价量、角度分辨强弱
- 后波涨势优于前波，持股抱紧
- 后波涨势弱于前波，择机清仓

**反向波看趋势**：
- 前波上涨波与本波下跌波比较，评估多空强弱抓趋势
- 下跌波陡于前后上涨波为转弱讯号
- 上涨波陡于前后下跌波为转强讯号

### 5.4 三生三世战法
**春江水暖鸭先知（买点）**：
- 连续三盘未破低为三盘不破
- 后一根K线突破前两根K线的高点即为黑翻红
- 在底部将完成或回档尾声时出现
- 搭配凹洞量、转折量、短期均线金叉效果更佳

**山雨欲来风满楼（卖点）**：
- 三根K线未创高为"三盘不过"
- 后一根K线破低且收黑即为红翻黑
- 在波段涨势进行过程中出现
- 第三根K线破前两根K线最低点属于危险信号

**实战要点**：
- 三盘分别指三根K线的最高价与最低价
- 波段高点与低点时，看多空的气势与买卖力道变化
- 连三根K线未创高，后一根K线创高，则为多头的中继中势

---

## 6. 仓位与风控体系

### 6.1 市场状态驱动仓位
- **牛市**：高仓位（如80-100%）
- **震荡市**：中等仓位（如30-60%）
- **熊市**：低仓位或空仓（如0-20%），可配债券、货币基金

### 6.2 风险控制
- **单票最大持仓**：如不超过总资金10%
- **板块/行业分散**：防止单一板块风险
- **黑名单过滤**：ST、退市、停牌、异常波动、历史大幅亏损等

### 6.3 均线系统风控
- **5日进攻法**：5日死叉10日就是卖出
- **均线反转**：5日均线反转包括价和量
- **触碰10日线次数**：第一次买，第二次持有，第三次准备撤退

---

## 7. 止盈止损与资金管理

### 7.1 动态止盈 - **收益最大化核心**
**核心原则**：动态止盈是实现收益最大化的关键，需要根据市场状态和个股特征灵活调整。

**具体策略**：
- **基础止盈**：最高点回撤5-10%、分批止盈、浮动止盈
- **技术止盈**：高位放量长阳低点不能破，一破就会泄洪
- **乖离止盈**：月乖离正数过大为卖出动作
- **月乖离参考标准**：
  - 大盘股7-10%
  - 一般个股15-20%
  - 小型股50%以上
- **涨停板止盈**：涨停打开立即卖出，避免回撤
- **趋势止盈**：DMI指标转弱或ADX跌破ADXR时考虑止盈

### 7.2 动态止损 - **风险最小化核心**
**核心原则**：动态止损是控制风险的关键，需要快速响应市场变化。

**具体策略**：
- **基础止损**：最大回撤5-8%、跌破关键均线、分批止损
- **形态止损**：三生三世：第三根K线破前两根K线最低点
- **趋势止损**：上升趋势线跌漏就是卖点
- **乖离止损**：月乖离负数过大为买进动作：
  - 大盘股负7-10%
  - 一般个股负10-15%
  - 小型个股负15-20%
- **DMI止损**：PDI跌破MDI且ADX转弱时止损
- **量价止损**：放量下跌或缩量上涨时止损

### 7.3 组合整体风控
- 总回撤超阈值自动减仓
- 均量线反转代表均线会反转

### 7.4 资金分配
- 等权、风险平价、波动率加权等

---

## 8. 选股策略与股票池管理

### 8.1 股票池分类
**追踪池**：
- 具备妖股、彪股基因，尚无明显发动迹象的个股
- 波段上涨后进入中期整理的个股
- 底部构筑尾声，可望启动涨势的个股

**操作池**：
- 按指标进攻法往上进阶的个股
- 中期整理或底部完成将启动的个股
- 均线、价量展现起步迹象的个股

### 8.2 选股条件
**技术面**：
- 均线5、10、20、60线上方的龙头股
- 股性交易量大活跃，区间换手率高
- 具备持续性题材、热点

**基本面**：
- 股息率2.75%以上
- 市净率破净
- 市盈率10以下（20以下可接受）

### 8.3 板块轮动策略
**强势板块特征**：
- 月均线多头排列
- 1年线>2年线>5年线>12年线
- 量价结构健康

**板块选择优先级**：
1. 月线双金：证券、水务、石油
2. 月均线多头：煤炭、银行、交通设施等
3. 周线多头排列：中字头、国资云、银行等

**底部回升值得关注的板块**：
- **月均线多头排列**：煤炭（年）、银行、交通设施、运输服务、通用机械、旅游、软件服务、中特估
- **即将转强**：酿酒、家用电器
- **破趋势向上的10年线**：深证成指、创业板、沪深300、上证50、房地产、水务、交通设施、仓储物流、运输设备、文教休闲、综合类、纺织服饰、广告包装、商贸代理、造纸、家居用品
- **周线多头排列**：中字头、国资云、银行、保险、证券、家用电器、通信设备、水务、石油、电力、运输设备、纺织服饰、工程机械、广告包装、船舶

**长多躺赢股选股条件**：
- 股息率2.75%以上
- 市净率破净
- 市盈率10以下（20以下可接受，超过20以上排除）
- 一般可以选出绩优股（稳定蓝筹）、成长稳定的个股

---

## 9. 实战操作技巧

### 9.1 多时间框架K线分析 - **精确买卖点核心**
**核心思想**：通过多时间框架K线分析来确定真正的买卖点，避免单一时间框架的局限性。这是确定精确买卖时机的关键方法。

**时间框架组合及作用**：
- **日线**：主要趋势判断，决定大方向和持仓策略
- **60分钟线**：中期趋势判断，决定波段操作和仓位调整
- **30分钟线**：短期趋势判断，决定具体买卖时机
- **15分钟线**：超短期趋势判断，决定精确买入点
- **5分钟线**：极短期趋势判断，决定精确卖出点

**重要说明**：真正的买卖决策需要通过多时间框架K线综合判断，单一时间框架容易产生误判。

**多时间框架买卖点确认**：
1. **买点确认流程**：
   - **日线**：价格站稳5日均线，均线多头排列
   - **60分钟线**：价格突破60分钟MA20，成交量放大
   - **30分钟线**：价格在30分钟MA10上方，KD金叉
   - **15分钟线**：价格突破15分钟MA5，量价配合
   - **5分钟线**：价格在5分钟MA5上方，准备买入

2. **卖点确认流程**：
   - **日线**：价格跌破5日均线，均线开始空头排列
   - **60分钟线**：价格跌破60分钟MA20，成交量萎缩
   - **30分钟线**：价格跌破30分钟MA10，KD死叉
   - **15分钟线**：价格跌破15分钟MA5，量价背离
   - **5分钟线**：价格跌破5分钟MA5，准备卖出

**多时间框架共振**：
- **强势买点**：所有时间框架都显示买入信号
- **强势卖点**：所有时间框架都显示卖出信号
- **分歧处理**：以较大时间框架为准，较小时间框架作为辅助

### 9.2 短线操作
**量比应用**：
- 0.5以下：主力高度控盘，风险很大
- 0.8-1.2：正常水平，风险机会皆不大
- 1.2-2.5：温和放量，趋势不变
- 2.5-5：明显放量，突破压力位看涨
- 5-10：剧烈放量，低位待涨，高位出货
- 10-20：过度放量，反转讯号

**换手率应用**：
- 1-3%：平量，以整理为主轴
- 3-5%：相对活跃，有效突破
- 5-15%：高度活跃
- >15%：过度活跃，筹码很乱

**量比详细解读**：
- 0.5以下：主力高度控盘，要上去还是下来搞不清楚，风险很大
- 0.5-1：涨停板代表后面会有继续涨停的可能，跌停板代表后面可能还会继续跌停
- 0.8-1.2：代表风险不大
- 1.2-2.5：代表多头放量或者空头放量，趋势维持
- 2.5-5：明显放量，如果到达重要位置可能会带量突破
- 5-10：如果在长期下降的情况下出现，代表见底（如果在高位出现就是见顶）
- 10-20：在上升趋势的时候接近高点（下跌趋势就是止跌回升）

**换手率详细解读**：
- 小于1%：没人关注或者高度集中
- 1-3%：平量，以整理为主轴，没什么变化
- 3-5%：相对活跃，往上有效突破，往下有效跌破
- 5-15%：高度活跃
- 大于15%：过度活跃，代表筹码很乱，要高度小心

### 9.2 波段操作
**买点确认**：
- 带量长红
- 突破短期压力线
- 回测到5日均线或10日均线时进场
- 凹洞量出现后价涨量增

**卖点确认**：
- 高位放量长阳或长阴
- 高位滞涨
- 宽幅震荡
- 跌5%且5日线反转

### 9.3 牛股妖股操作
**特征**：
- 沿着5日均线上攻，持续3-4周
- 最坏情况修正到20日线
- 牛股和妖股修复到10日线

**操作策略**：
- 涨了3-4周不要去追
- 可在10日均线和20日线做布局
- 高位放量不管是长阳还是长阴都是卖讯

**5日进攻法适用场景**：
- 波段起涨
- 主升段
- 整理洗盘后

**触碰10日线次数规则**：
- 第一次：买
- 第二次：持有
- 第三次：准备撤退

**均线反转信号**：
- 5日线反转包括价和量
- 均量线反转代表均线会反转

---

## 10. 黑名单与异常处理

### 10.1 严格过滤
- ST、退市、停牌、黑名单、异常波动、财务造假、重大负面等
- 自动剔除：持仓中出现黑名单信号，自动清仓

### 10.2 风险警示信号
- 高位爆量和凹洞量出现，8成以上会往下走
- 第三根跌漏前面两根K线的最低点（三生三世）
- 均量线开始缩量反转向下
- 均价线开始忽上忽下，不再上升

**头部确立信号**：
- ENE指标打上轨
- KD指标月乖离正乖离过大（进入风险阶段）
- 大单进价不涨（警讯阶段）
- 大量低点跌漏（危机阶段）
- 五日均线扣抵低点跌漏和十日均线扣抵低点跌漏（破坏阶段）

**顶部特征**：
- 量增价不涨，短线均量开始萎缩
- K线阴线多了，长上影也多
- KD指标背离
- 上升趋势线跌漏
- 高档位出现凹洞量是坏事

### 10.3 底部特征与买点
**底部特征**：
- 价稳量缩，短期均线均量开始止跌回升（5日均线和10日均线开始回升出现金叉）
- 日K线长下影线中长红相继出现（阳线变多，阴线变少）
- KD指标出现背离或者横向的现象
- 突破下降的短均线，短均线开始回升，没有再破短均线

**底部买点确认**：
- 带量长红
- 突破短期压力线
- 回测到5日均线或者10日均线的时候，可以进场买

**底部转强模式**：
- 先画趋势通道线（如果股价突破回踩就不能再进入通道线内）
- 任何型态的突破必须是增量，过型态后不能再回来
- 攻击每一根下降均线的时候必须堆量
- 一个型态中低点不断上移是上涨常态
- 一般长均线向上或者走平都不是压力
- 多头型态：一般都是均线多头，下跌缩量，上涨放量

---

## 11. 量化策略实现框架

### 11.1 策略架构设计
**核心模块**：
1. **数据获取模块**：实时行情数据、历史数据、基本面数据
2. **市场状态判断模块**：基于均线、DMI、量价结构判断牛熊震荡
3. **选股模块**：多因子选股、板块轮动、股票池管理
4. **信号生成模块**：技术指标信号、买卖点判断
5. **风控模块**：仓位控制、止损止盈、风险监控
6. **执行模块**：订单管理、交易执行、持仓管理

**技术栈建议**：
- **编程语言**：Python（pandas, numpy, talib）
- **数据源**：tushare、akshare、wind、choice
- **回测框架**：backtrader、zipline、聚宽、米筐
- **实盘接口**：各券商API、CTP、易盛等

### 11.2 核心算法实现
**市场风险判断算法**：
```python
def judge_market_risk():
    """
    基于沪深300和富时A50期指判断市场风险
    输出：'high', 'medium', 'low' 风险等级
    """
    # 获取沪深300数据
    hs300_data = get_stock_data('000300')
    # 获取富时A50期指数据
    a50_data = get_stock_data('CN0Y')
    
    # 沪深300风险判断
    hs300_ma60 = hs300_data['close'].rolling(60).mean()
    hs300_risk = 'low'
    if hs300_data['close'].iloc[-1] < hs300_ma60.iloc[-1] and hs300_ma60.iloc[-1] < hs300_ma60.iloc[-5]:
        hs300_risk = 'high'
    elif hs300_data['close'].iloc[-1] < hs300_ma60.iloc[-1]:
        hs300_risk = 'medium'
    
    # A50期指风险判断
    a50_risk = 'low'
    if a50_data['close'].iloc[-1] < a50_data['close'].iloc[-5] and a50_data['volume'].iloc[-1] > a50_data['volume'].rolling(5).mean().iloc[-1]:
        a50_risk = 'high'
    elif a50_data['close'].iloc[-1] < a50_data['close'].iloc[-5]:
        a50_risk = 'medium'
    
    # 综合风险判断
    if hs300_risk == 'high' or a50_risk == 'high':
        return 'high'
    elif hs300_risk == 'medium' or a50_risk == 'medium':
        return 'medium'
    else:
        return 'low'
```

**强势板块识别算法**：
```python
def identify_strong_sectors():
    """
    识别强势板块
    输出：强势板块列表
    """
    strong_sectors = []
    
    # 获取所有板块数据
    sectors = get_all_sectors()
    
    for sector in sectors:
        sector_data = get_sector_data(sector)
        
        # 板块涨幅排名前20%
        sector_return = (sector_data['close'].iloc[-1] - sector_data['close'].iloc[-2]) / sector_data['close'].iloc[-2]
        if sector_return < 0.02:  # 假设前20%涨幅为2%以上
            continue
            
        # 板块内涨停数量≥3只
        limit_up_count = count_limit_up_stocks(sector)
        if limit_up_count < 3:
            continue
            
        # 板块成交量放大50%以上
        volume_ratio = sector_data['volume'].iloc[-1] / sector_data['volume'].rolling(5).mean().iloc[-1]
        if volume_ratio < 1.5:
            continue
            
        # 板块指数站稳5日均线
        ma5 = sector_data['close'].rolling(5).mean()
        if sector_data['close'].iloc[-1] < ma5.iloc[-1]:
            continue
            
        strong_sectors.append(sector)
    
    return strong_sectors
```

**强势个股识别算法**：
```python
def identify_strong_stocks(strong_sectors):
    """
    从强势板块中识别强势个股
    输入：强势板块列表
    输出：强势个股列表
    """
    strong_stocks = []
    
    for sector in strong_sectors:
        stocks = get_sector_stocks(sector)
        
        for stock in stocks:
            stock_data = get_stock_data(stock)
            
            # 个股涨幅排名前30%
            stock_return = (stock_data['close'].iloc[-1] - stock_data['close'].iloc[-2]) / stock_data['close'].iloc[-2]
            if stock_return < 0.01:  # 假设前30%涨幅为1%以上
                continue
                
            # 个股成交量放大30%以上
            volume_ratio = stock_data['volume'].iloc[-1] / stock_data['volume'].rolling(5).mean().iloc[-1]
            if volume_ratio < 1.3:
                continue
                
            # 个股站稳5日均线
            ma5 = stock_data['close'].rolling(5).mean()
            if stock_data['close'].iloc[-1] < ma5.iloc[-1]:
                continue
                
            strong_stocks.append(stock)
    
    return strong_stocks
```

**多时间框架买卖点确认算法**：
```python
def confirm_trading_signals(stock):
    """
    多时间框架确认买卖点
    输入：股票代码
    输出：'buy', 'sell', 'hold'
    """
    # 获取多时间框架数据
    daily_data = get_stock_data(stock, period='1d')
    h60_data = get_stock_data(stock, period='60m')
    h30_data = get_stock_data(stock, period='30m')
    h15_data = get_stock_data(stock, period='15m')
    m5_data = get_stock_data(stock, period='5m')
    
    # 日线判断
    daily_ma5 = daily_data['close'].rolling(5).mean()
    daily_signal = 'hold'
    if daily_data['close'].iloc[-1] > daily_ma5.iloc[-1]:
        daily_signal = 'buy'
    elif daily_data['close'].iloc[-1] < daily_ma5.iloc[-1]:
        daily_signal = 'sell'
    
    # 60分钟线判断
    h60_ma20 = h60_data['close'].rolling(20).mean()
    h60_signal = 'hold'
    if h60_data['close'].iloc[-1] > h60_ma20.iloc[-1] and h60_data['volume'].iloc[-1] > h60_data['volume'].rolling(5).mean().iloc[-1]:
        h60_signal = 'buy'
    elif h60_data['close'].iloc[-1] < h60_ma20.iloc[-1]:
        h60_signal = 'sell'
    
    # 30分钟线判断
    h30_ma10 = h30_data['close'].rolling(10).mean()
    h30_k, h30_d = calculate_kd(h30_data)
    h30_signal = 'hold'
    if h30_data['close'].iloc[-1] > h30_ma10.iloc[-1] and h30_k.iloc[-1] > h30_d.iloc[-1]:
        h30_signal = 'buy'
    elif h30_data['close'].iloc[-1] < h30_ma10.iloc[-1] and h30_k.iloc[-1] < h30_d.iloc[-1]:
        h30_signal = 'sell'
    
    # 15分钟线判断
    h15_ma5 = h15_data['close'].rolling(5).mean()
    h15_signal = 'hold'
    if h15_data['close'].iloc[-1] > h15_ma5.iloc[-1]:
        h15_signal = 'buy'
    elif h15_data['close'].iloc[-1] < h15_ma5.iloc[-1]:
        h15_signal = 'sell'
    
    # 5分钟线判断
    m5_ma5 = m5_data['close'].rolling(5).mean()
    m5_signal = 'hold'
    if m5_data['close'].iloc[-1] > m5_ma5.iloc[-1]:
        m5_signal = 'buy'
    elif m5_data['close'].iloc[-1] < m5_ma5.iloc[-1]:
        m5_signal = 'sell'
    
    # 综合判断
    buy_count = sum([1 for signal in [daily_signal, h60_signal, h30_signal, h15_signal, m5_signal] if signal == 'buy'])
    sell_count = sum([1 for signal in [daily_signal, h60_signal, h30_signal, h15_signal, m5_signal] if signal == 'sell'])
    
    if buy_count >= 4:  # 至少4个时间框架显示买入
        return 'buy'
    elif sell_count >= 4:  # 至少4个时间框架显示卖出
        return 'sell'
    else:
        return 'hold'
```

**市场状态判断算法**：
```python
def judge_market_state(df):
    """
    判断市场状态：牛市/熊市/震荡市
    输入：DataFrame包含OHLCV数据
    输出：'bull', 'bear', 'sideways'
    """
    # 计算均线
    df['MA5'] = df['close'].rolling(5).mean()
    df['MA10'] = df['close'].rolling(10).mean()
    df['MA20'] = df['close'].rolling(20).mean()
    df['MA60'] = df['close'].rolling(60).mean()
    
    # 计算DMI指标
    df = calculate_dmi(df, n=14, m=6)
    
    # 判断条件
    current_price = df['close'].iloc[-1]
    ma60 = df['MA60'].iloc[-1]
    ma60_slope = (df['MA60'].iloc[-1] - df['MA60'].iloc[-5]) / 5
    
    pdi = df['PDI'].iloc[-1]
    mdi = df['MDI'].iloc[-1]
    adx = df['ADX'].iloc[-1]
    
    # 牛市判断
    if (current_price > ma60 and ma60_slope > 0 and 
        pdi > mdi and adx > 55):
        return 'bull'
    # 熊市判断
    elif (current_price < ma60 and ma60_slope < 0 and 
          mdi > pdi and adx > 55):
        return 'bear'
    # 震荡市判断
    else:
        return 'sideways'
```

**技术指标计算函数**：
```python
def calculate_kd(df, n=9, m1=3, m2=3):
    """计算KD指标"""
    low_min = df['low'].rolling(n).min()
    high_max = df['high'].rolling(n).max()
    
    rsv = (df['close'] - low_min) / (high_max - low_min) * 100
    k = rsv.ewm(span=m1).mean()
    d = k.ewm(span=m2).mean()
    
    return k, d

def calculate_dmi(df, n=14, m=6):
    """计算DMI指标"""
    # 计算真实波幅
    tr1 = df['high'] - df['low']
    tr2 = abs(df['high'] - df['close'].shift(1))
    tr3 = abs(df['low'] - df['close'].shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算方向移动
    hd = df['high'] - df['high'].shift(1)
    ld = df['low'].shift(1) - df['low']
    
    # 正向和负向移动
    pdm = np.where((hd > ld) & (hd > 0), hd, 0)
    ndm = np.where((ld > hd) & (ld > 0), ld, 0)
    
    # 计算平滑值
    tr_smooth = tr.rolling(n).sum()
    pdm_smooth = pd.Series(pdm).rolling(n).sum()
    ndm_smooth = pd.Series(ndm).rolling(n).sum()
    
    # 计算PDI和MDI
    pdi = pdm_smooth / tr_smooth * 100
    mdi = ndm_smooth / tr_smooth * 100
    
    # 计算ADX
    dx = abs(pdi - mdi) / (pdi + mdi) * 100
    adx = dx.rolling(m).mean()
    adxr = (adx + adx.shift(m)) / 2
    
    df['PDI'] = pdi
    df['MDI'] = mdi
    df['ADX'] = adx
    df['ADXR'] = adxr
    
    return df
```

**选股算法**：
```python
def select_stocks(stock_pool, market_state):
    """
    根据市场状态选股
    输入：股票池、市场状态
    输出：选中的股票列表
    """
    selected_stocks = []
    
    for stock in stock_pool:
        # 获取股票数据
        df = get_stock_data(stock)
        
        # 计算技术指标
        df = calculate_indicators(df)
        
        # 根据市场状态应用不同选股策略
        if market_state == 'bull':
            if is_bull_stock(df):
                selected_stocks.append(stock)
        elif market_state == 'bear':
            if is_bear_stock(df):
                selected_stocks.append(stock)
        else:  # sideways
            if is_sideways_stock(df):
                selected_stocks.append(stock)
    
    return selected_stocks

def is_bull_stock(df):
    """判断是否为牛市股票"""
    # 均线多头排列
    ma_condition = (df['MA5'].iloc[-1] > df['MA10'].iloc[-1] > 
                   df['MA20'].iloc[-1] > df['MA60'].iloc[-1])
    
    # DMI多头
    dmi_condition = (df['PDI'].iloc[-1] > df['MDI'].iloc[-1] and 
                    df['ADX'].iloc[-1] > 30)
    
    # 量价配合
    volume_condition = df['volume'].iloc[-1] > df['volume'].rolling(5).mean().iloc[-1]
    
    return ma_condition and dmi_condition and volume_condition
```

### 11.2 交易执行关键要点

**买卖价格策略**：
- **买入策略**：使用卖一价（sell1）买入，确保能够成交
- **卖出策略**：使用买一价（buy1）卖出，确保能够成交
- **止损策略**：使用买一价*0.99快速止损，确保成交
- **止盈策略**：使用买一价止盈，获得较好价格

**订单管理策略**：
- **避免数量错误**：使用`order_value()`函数按金额下单，避免`order_target()`的数量计算错误
- **订单超时处理**：5分钟未成交订单自动撤销并重新委托（最快时间）
- **重新委托策略**：使用市价单重新委托，确保成交
- **检查频率**：每5分钟检查一次订单状态，确保快速处理
- **数量限制**：确保下单数量为100的整数倍

**聚宽平台特殊注意事项**：
- 使用`order_value(security, value, price=buy_price)`按金额买入
- 使用`order_target_percent(security, 0, price=sell_price)`按比例卖出
- 避免使用`order_target()`的数量计算，容易出现"开仓数量不能小于100"错误
- 定期检查未成交订单状态，及时处理超时订单

**常见错误及解决方案**：
- **错误**：`开仓数量不能小于100` 和 `开仓数量必须是100的整数倍` 矛盾
- **原因**：使用`order_target()`时数量计算错误
- **解决**：改用`order_value()`按金额下单，让系统自动计算正确数量
- **错误**：委托失败，订单无法成交
- **原因**：价格设置不合理，没有使用实时买卖价格
- **解决**：使用卖一价买入、买一价卖出，提高成交率
- **错误**：`委托单买不到`
- **原因**：价格设置过于复杂或市场流动性不足
- **解决**：简化下单逻辑，使用市价单或让平台自动处理价格

### 11.3 风控系统实现
```python
class RiskManager:
    def __init__(self, max_position_pct=0.1, max_drawdown=0.1):
        self.max_position_pct = max_position_pct  # 单票最大仓位
        self.max_drawdown = max_drawdown  # 最大回撤
        
    def calculate_position_size(self, capital, stock_price, risk_score):
        """计算仓位大小"""
        max_position_value = capital * self.max_position_pct
        position_size = max_position_value / stock_price
        return int(position_size * risk_score)  # 根据风险评分调整
    
    def check_stop_loss(self, current_price, buy_price, stop_loss_pct=0.08):
        """检查止损"""
        loss_pct = (buy_price - current_price) / buy_price
        return loss_pct > stop_loss_pct
    
    def check_take_profit(self, current_price, buy_price, take_profit_pct=0.15):
        """检查止盈"""
        profit_pct = (current_price - buy_price) / buy_price
        return profit_pct > take_profit_pct
```

### 11.4 可扩展与升级方向

#### 11.4.1 技术指标优化
- 增加机器学习/AI因子（如XGBoost、随机森林、深度学习等）
- 引入更多基本面、资金面、情绪面因子
- 优化择时、轮动、风控、止盈止损算法

#### 11.4.2 系统扩展
- 支持多市场、多周期、多品种扩展
- 实盘与回测一体化、参数自动寻优
- 实时监控与预警系统

#### 11.4.3 策略升级
- 基于历史数据的策略回测与优化
- 动态参数调整机制
- 多策略组合管理

---

## 12. 高收益策略成功要素分析

### 12.1 参考策略成功要素（基于5年15倍收益策略）
**核心成功要素分析**：
1. **简单有效的选股逻辑**：
   - **基本面筛选**：ROE>15%, ROA>10%（确保盈利能力）
   - **小市值排序**：优先选择小市值股票（成长空间大）
   - **低价股筛选**：股价<10元（降低风险，提高安全边际）

2. **严格的过滤机制**：
   - **过滤ST、退市、停牌股票**：避免基本面风险
   - **过滤次新股**：上市不足250天，避免估值过高
   - **过滤高价股**：>10元，控制单股风险
   - **过滤涨跌停板股票**：避免追高杀跌

3. **涨停板特殊处理机制**：
   - **涨停股票继续持有**：让利润奔跑
   - **涨停打开立即卖出**：避免大幅回撤
   - **避免重复买入近期涨停股票**：防止追高

4. **黑名单机制**：
   - **记录N天内持有过的股票**：避免频繁交易同一股票
   - **避免重复买入**：提高资金利用效率
   - **动态更新黑名单**：保持名单的时效性

5. **调仓策略优化**：
   - **原策略**：月度调仓，避免过度交易
   - **本策略改进**：动态调仓，强者恒强，弱者淘汰
   - **平衡考虑**：在交易成本和收益最大化之间找平衡

### 12.2 策略优化建议 - **融合高收益要素**
**基于参考策略的改进方向**：
1. **选股逻辑优化**：
   - **保留简单有效的基本面筛选**：ROE>15%, ROA>10%
   - **结合强势板块选股**：从强势板块中选择符合基本面条件的个股
   - **增加DMI趋势确认**：确保选中的个股处于上升趋势

2. **过滤机制强化**：
   - **保留原有过滤条件**：ST、停牌、次新股、高价股等
   - **增加技术面过滤**：DMI空头、均线空头排列等
   - **增加市场风险过滤**：大盘高风险期降低选股标准

3. **止盈止损优化**：
   - **保留涨停板处理机制**：涨停持有，打开卖出
   - **结合DMI动态止盈止损**：趋势强势持有，转弱立即止损
   - **多时间框架确认**：避免单一信号误判

4. **调仓机制改进**：
   - **从月度调仓改为动态调仓**：强弱决定调仓频率
   - **保留黑名单机制**：避免频繁交易同一股票
   - **增加板块轮动**：及时切换到新的强势板块

5. **风险控制增强**：
   - **保留基本面风险控制**：财务指标筛选
   - **增加市场风险控制**：沪深300+A50期指双重确认
   - **增加技术风险控制**：DMI+均线+多时间框架确认

## 13. 参考资料与笔记精华

### 12.1 核心理论来源
- 《王者之声》郭海培老师笔记（均线、扣抵、量价、角度、波段、DMI/KD等核心思想）
- 《胜者为王》郭海培老师笔记（实战操作技巧、选股策略、风控体系）
- 通达信副图指标源码（KD、DMI、ENE）

### 12.2 实战经验总结
- 历年聚宽、国信iQuant等平台实盘与回测策略
- 量化社区、研报、经典书籍等
- 郭海培老师实战交易体系精华

### 12.3 关键操作要点
- **赢家特质**：严控风险、严守停损、严守停利；策略灵活，严控资金；顺势而为，着重价值投资；做好资产配置，降低风险
- **输家特质**：只看报酬，只算收益；一旦套牢，就放着不管；贪心，对报酬率要求一再提高；一路作多或一路作空；一心赚更多，却过度举债投资；逆势操作，空头还要抢反弹财

**新兴市场教父莫比尔斯十大投资法则**：
1. 评估股市投资时，排除情绪因素，将策略回归长期基本面，就能战胜大盘
2. 股价下挫20%，或从高点大幅重挫，投资价值浮现时，应赶紧建立部位
3. 新兴市场的投资绝非易事，但采取逆势操作终究能投资成功
4. 股市或个股在短期因素大跌，但其基本面未变的话，立刻买进
5. 金融风暴中，新兴市场先遭到打击，通常也率先复苏
6. 将新兴市场货币波动的不利因素，转成潜在获利机会
7. 市场最悲观的时候，就是进场点
8. 下跌的市场终究会回升，如果有耐心，就无须恐慌
9. 逢低布局，基金表现可能落后，但也是未来涨幅领先大盘的契机
10. 请耐心投资五年

**抢短的心态、选股与操作**：
- 永远不满仓
- 风险大于机会，不确定方向选择空仓
- 跟随主赛道，风口，热点的节奏
- 在非牛市不要抱热点股票
- 追热点方向，热钱方向，立即上车，后期不追
- 新板块会热一段

### 12.4 开发注意事项
- **数据质量**：确保历史数据的完整性和准确性
- **参数优化**：通过回测优化各项技术指标参数
- **实盘测试**：先进行模拟盘测试，再逐步过渡到实盘
- **风险控制**：严格执行止损止盈，控制单票和整体仓位
- **持续优化**：根据市场变化和策略表现持续调整优化

---

> **重要声明**：本文档为本量化策略的完整设计蓝图和开发说明，融合了郭海培老师《王者之声》和《胜者为王》的核心交易理念，以及5年15倍高收益策略的成功要素分析。**任何AI或开发者只需阅读本.md文件，即可完全理解并复现/升级本策略**。
> 
> **文档完整性确认**：
> - ✅ 包含完整的理论体系（均线、量价、角度、波段、DMI/KD等）
> - ✅ 包含详细的实战技巧和操作要点
> - ✅ 包含完善的风控机制和止盈止损策略
> - ✅ 包含DMI指标的重要性说明（最老实指标，无背离）
> - ✅ 包含动态止盈止损的详细策略（收益最大化核心）
> - ✅ 包含高收益策略的成功要素分析
> - ✅ 包含完整的量化策略实现框架和代码示例
> - ✅ 包含交易执行的关键要点和常见错误解决方案
> - ✅ 包含市场风险判断机制（沪深300+富时A50期指）
> - ✅ 包含强势板块和强势个股动态识别机制
> - ✅ 包含多时间框架K线分析（日线、60分钟、30分钟、15分钟、5分钟）
> - ✅ 包含均K线和均量线作为主要决策依据的说明
> - ✅ 包含KD指标背离风险的重要提醒
> 
> **策略特色**：
> - **多层级决策体系**：基于"先大盘、再板块、再个股"的科学决策流程
> - **双指数风险控制**：以沪深300和富时A50期指为核心的市场风险判断
> - **动态强势管理**：每日动态识别和更新强势板块、强势个股自定义板块
> - **多时间框架精确定位**：通过日线、60分钟、30分钟、15分钟、5分钟K线确定精确买卖点
> - **科学指标优先级**：DMI指标（第一优先级，最可靠无背离）→均K线均量线（第二优先级）→KD指标（第三优先级，注意背离）
> - **动态调仓机制**：不固定调仓周期，强者恒强，弱者淘汰
> - **动态止盈止损**：让利润奔跑，快速止损，实现收益最大化
> - **高收益要素融合**：结合5年15倍收益策略的成功要素
> - **完整风控体系**：涨停板处理、黑名单机制、严格股票筛选
> 
> 本策略具备完整的理论体系、实战技巧和风控机制，可直接应用于实盘交易，并具备持续优化和升级的能力。

---

十四、策略核心要点总结 - **AI开发必读**

A、核心策略流程
**完整策略执行流程**：
1. **市场风险判断**：通过沪深300和富时A50期指判断市场风险，控制整体仓位
2. **强势板块识别**：每日识别强势板块，创建并更新"强势板块"自定义板块
3. **强势个股筛选**：从强势板块中筛选强势个股，创建并更新"强势个股"自定义板块
4. **多时间框架确认**：通过日线、60分钟、30分钟、15分钟、5分钟K线确定买卖点
5. **动态调仓执行**：根据强弱变化进行动态调仓，不固定调仓周期

### 14.2 关键技术指标优先级
**指标重要性排序**：
1. **第一优先级：DMI指标**
   - **最重要的核心指标**，最老实可靠，**无任何背离**
   - PDI>MDI且ADX>55：强势持有
   - PDI<MDI：立即止损
   - 是买卖决策的最重要依据

2. **第二优先级：均K线和均量线**
   - **主要决策依据**，价格和成交量的趋势判断
   - 均K线：5日、10日、20日、60日均线
   - 均量线：5日、10日均量线
   - 均量线反转预示均线将反转

3. **第三优先级：KD指标**
   - **仅作辅助判断**，存在背离现象
   - **重要提醒**：不能单纯以KD过热而卖出股票
   - 只有当KD过热且K线走平、量能萎缩时才考虑卖出
   - 只要K线和成交量能跟上，即使KD过热也要持有

### 14.3 动态止盈止损核心
**收益最大化关键**：
- **动态止盈**：不设固定止盈点，根据DMI趋势强弱决定
- **动态止损**：快速响应趋势转弱信号，严格执行止损
- **涨停板处理**：涨停继续持有，打开立即卖出
- **让利润奔跑**：趋势强势时坚决持有，不轻易止盈

### 14.4 调仓机制核心
**灵活应变关键**：
- **不固定调仓周期**：完全根据强弱变化决定
- **强者恒强原则**：强势股继续持有，弱势股立即淘汰
- **板块个股联动**：板块轮动时及时调整个股持仓
- **快进快出**：发现强势立即买入，发现转弱立即卖出

### 14.5 AI开发重点提醒
**开发策略时必须注意**：
1. **DMI指标是核心**：必须作为最重要的判断依据
2. **KD指标有背离**：不能单纯以过热卖出，要结合K线和量能
3. **多时间框架确认**：避免单一时间框架误判
4. **动态调仓机制**：不要固定月度调仓，要根据强弱动态调整
5. **强势板块个股管理**：每日更新强势板块和强势个股自定义板块
6. **市场风险控制**：通过沪深300和A50期指控制整体仓位

--