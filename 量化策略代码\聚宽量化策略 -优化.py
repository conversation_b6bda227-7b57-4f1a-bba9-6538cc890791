# -*- coding: utf-8 -*-
"""
聚宽量化策略 - 基于四层级板块管理的多层级决策体系
策略核心：先大盘、再板块、再个股 + 智能委托管理
融合郭海培老师《王者之声》和《胜者为王》的实战交易体系
"""

import numpy as np
import pandas as pd
import talib
from datetime import datetime, timedelta

def initialize(context):
    """初始化函数"""
    # 设置基准
    set_benchmark('000300.XSHG')
    # 设置滑点
    set_slippage(FixedSlippage(0.02))
    # 设置交易成本
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, 
                            open_commission=0.0003, close_commission=0.0003, 
                            close_today_commission=0, min_commission=5), type='fund')
    # 开启动态复权模式
    set_option('use_real_price', True)
    # 设置避免未来数据
    set_option('avoid_future_data', True)

    # 策略参数
    g.stock_num = 10  # 持股数量
    g.max_position_pct = 0.1  # 单票最大仓位
    g.stop_loss_pct = 0.08  # 止损比例
    g.take_profit_pct = 0.15  # 止盈比例

    # 四层级板块管理
    g.strong_sectors = []  # 强势指数板块1
    g.sector_stocks = []   # 强势板块中的股票2
    g.strong_stocks_in_sector = []  # 板块中的强势股3
    g.strong_stocks_market = []     # 强势个股4

    # 委托管理 - 核心改进
    g.pending_orders = {}  # 待处理订单
    g.order_retry_count = {}  # 订单重试次数
    g.max_retry = 3  # 最大重试次数
    g.order_check_interval = 3  # 检查间隔（秒）

    # 黑名单管理
    g.blacklist = []  # 黑名单股票
    g.just_sold = []  # 刚卖出的股票

    # 市场风险控制
    g.market_risk_level = 'medium'
    g.max_position_ratio = 0.6

    # 定时任务
    run_daily(market_risk_check, time='9:05')  # 市场风险判断
    run_daily(update_four_level_sectors, time='9:10')  # 更新四层级板块
    run_daily(check_positions, time='9:30')  # 检查持仓
    run_daily(check_limit_up, time='14:00')  # 检查涨停
    run_daily(adjust_positions, time='14:50')  # 调仓

    # 委托状态检查（每3秒）
    run_daily(check_order_status, time='9:30:03')
    run_daily(check_order_status, time='9:30:06')
    run_daily(check_order_status, time='9:30:09')
    # ... 可以添加更多时间点

def check_order_status(context):
    """检查委托状态 - 3秒检查机制"""
    current_time = context.current_dt
    orders = get_open_orders()

    for stock, order_list in orders.items():
        for order in order_list:
            order_id = order.order_id

            # 检查订单是否在监控列表中
            if order_id not in g.pending_orders:
                # 新订单，加入监控
                g.pending_orders[order_id] = {
                    'time': current_time,
                    'stock': stock,
                    'retry_count': 0
                }
                continue

            # 检查订单是否超过3秒未成交
            elapsed_time = (current_time - g.pending_orders[order_id]['time']).total_seconds()

            if elapsed_time >= g.order_check_interval:  # 3秒未成交
                log.info(f"订单超时未成交: {stock}, 订单ID: {order_id}, 已等待: {elapsed_time:.1f}秒")
                
                # 撤单
                cancel_result = cancel_order(order)
                if cancel_result:
                    log.info(f"成功撤销订单: {stock}, 订单ID: {order_id}")
                    
                    # 重新下单
                    retry_order(context, stock, order_id, order)
                else:
                    log.warning(f"撤单失败: {stock}, 订单ID: {order_id}")

                # 清理记录
                if order_id in g.pending_orders:
                    del g.pending_orders[order_id]

def retry_order(context, stock, original_order_id, original_order):
    """重新下单 - 智能价格调整"""
    # 检查重试次数
    if original_order_id not in g.order_retry_count:
        g.order_retry_count[original_order_id] = 0

    g.order_retry_count[original_order_id] += 1

    # 超过最大重试次数
    if g.order_retry_count[original_order_id] > g.max_retry:
        log.info(f"订单重试次数超限: {stock}, 放弃重试")
        return

    current_data = get_current_data()
    if stock not in current_data or current_data[stock].paused:
        log.info(f"股票停牌或数据异常: {stock}")
        return

    current_price = current_data[stock].last_price
    retry_count = g.order_retry_count[original_order_id]

    try:
        if original_order.side == 'long':  # 买入订单
            # 逐步提高买入价格
            price_adjustment = 1 + (retry_count * 0.005)  # 每次提高0.5%
            new_price = current_price * price_adjustment

            # 限制在涨停价以内
            limit_up_price = current_data[stock].high_limit
            if limit_up_price > 0:
                new_price = min(new_price, limit_up_price * 0.995)

            # 重新买入
            target_value = original_order.amount * original_order.price
            new_order = order_value(stock, target_value, style=LimitOrderStyle(new_price))

            if new_order:
                log.info(f"重新买入: {stock}, 第{retry_count}次重试, 新价格: {new_price:.2f}")
                # 记录新订单
                g.pending_orders[new_order.order_id] = {
                    'time': context.current_dt,
                    'stock': stock,
                    'retry_count': retry_count
                }
            else:
                log.warning(f"重新买入失败: {stock}")

        else:  # 卖出订单
            # 逐步降低卖出价格
            price_adjustment = 1 - (retry_count * 0.005)  # 每次降低0.5%
            new_price = current_price * price_adjustment

            # 限制在跌停价以上
            limit_down_price = current_data[stock].low_limit
            if limit_down_price > 0:
                new_price = max(new_price, limit_down_price * 1.005)

            # 重新卖出
            new_order = order_target_percent(stock, 0, style=LimitOrderStyle(new_price))

            if new_order:
                log.info(f"重新卖出: {stock}, 第{retry_count}次重试, 新价格: {new_price:.2f}")
                # 记录新订单
                g.pending_orders[new_order.order_id] = {
                    'time': context.current_dt,
                    'stock': stock,
                    'retry_count': retry_count
                }
            else:
                log.warning(f"重新卖出失败: {stock}")

    except Exception as e:
        log.error(f"重新下单异常: {stock}, 错误: {str(e)}")

def smart_order_buy(context, stock, target_value):
    """智能买入下单"""
    current_data = get_current_data()
    
    if stock not in current_data or current_data[stock].paused:
        log.info(f"股票停牌或数据异常，跳过买入: {stock}")
        return None

    current_price = current_data[stock].last_price
    
    # 买入策略：使用涨停价*0.99委托，确保能够成交
    limit_up_price = current_data[stock].high_limit
    if limit_up_price > 0:
        buy_price = limit_up_price * 0.99
    else:
        buy_price = current_price * 1.01

    # 使用order_value按金额下单，避免数量计算错误
    order = order_value(stock, target_value, style=LimitOrderStyle(buy_price))

    if order:
        # 记录订单到监控列表
        g.pending_orders[order.order_id] = {
            'time': context.current_dt,
            'stock': stock,
            'retry_count': 0
        }
        log.info(f"智能买入下单: {stock}, 目标金额: {target_value:.0f}, 委托价格: {buy_price:.2f}")
    else:
        log.warning(f"买入下单失败: {stock}")

    return order

def smart_order_sell(context, stock, reason='调仓'):
    """智能卖出下单"""
    current_data = get_current_data()
    
    if stock not in current_data or current_data[stock].paused:
        log.info(f"股票停牌或数据异常，跳过卖出: {stock}")
        return None

    current_price = current_data[stock].last_price
    
    # 卖出策略：根据原因选择不同价格
    if reason == '止损':
        # 止损：使用跌停价*1.01，确保快速成交
        limit_down_price = current_data[stock].low_limit
        if limit_down_price > 0:
            sell_price = limit_down_price * 1.01
        else:
            sell_price = current_price * 0.95
    else:
        # 正常卖出：使用跌停价*1.01
        limit_down_price = current_data[stock].low_limit
        if limit_down_price > 0:
            sell_price = limit_down_price * 1.01
        else:
            sell_price = current_price * 0.99

    # 卖出全部持仓
    order = order_target_percent(stock, 0, style=LimitOrderStyle(sell_price))

    if order:
        # 记录订单到监控列表
        g.pending_orders[order.order_id] = {
            'time': context.current_dt,
            'stock': stock,
            'retry_count': 0
        }
        log.info(f"智能卖出下单: {stock}, 原因: {reason}, 委托价格: {sell_price:.2f}")
    else:
        log.warning(f"卖出下单失败: {stock}")

    return order

def market_risk_check(context):
    """市场风险判断 - 基于沪深300和富时A50期指"""
    try:
        # 获取沪深300指数数据
        hs300_data = get_price('000300.XSHG', count=60, end_date=context.previous_date,
                              fields=['close', 'high', 'low', 'volume'])
        
        if len(hs300_data) < 60:
            return

        # 计算60日均线
        ma60 = hs300_data['close'].rolling(60).mean()
        current_price = hs300_data['close'].iloc[-1]
        current_ma60 = ma60.iloc[-1]
        prev_ma60 = ma60.iloc[-2]

        # 计算DMI指标
        dmi_signals = calculate_dmi(hs300_data)

        # 市场风险判断
        if (current_price > current_ma60 and current_ma60 > prev_ma60 and
            dmi_signals['pdi'] > dmi_signals['mdi'] and dmi_signals['adx'] > 55):
            g.market_risk_level = 'low'
            g.max_position_ratio = 1.0
        elif current_price > current_ma60 * 0.98:
            g.market_risk_level = 'medium'
            g.max_position_ratio = 0.6
        else:
            g.market_risk_level = 'high'
            g.max_position_ratio = 0.2

        log.info(f"市场风险等级: {g.market_risk_level}, 最大仓位: {g.max_position_ratio}")

    except Exception as e:
        log.error(f"市场风险评估异常: {str(e)}")

def calculate_dmi(stock_data, n=14):
    """计算DMI指标"""
    try:
        high = stock_data['high'].values
        low = stock_data['low'].values
        close = stock_data['close'].values
        
        pdi = talib.PLUS_DI(high, low, close, timeperiod=n)
        mdi = talib.MINUS_DI(high, low, close, timeperiod=n)
        adx = talib.ADX(high, low, close, timeperiod=n)
        
        return {
            'pdi': pdi[-1] if len(pdi) > 0 and not np.isnan(pdi[-1]) else 0,
            'mdi': mdi[-1] if len(mdi) > 0 and not np.isnan(mdi[-1]) else 0,
            'adx': adx[-1] if len(adx) > 0 and not np.isnan(adx[-1]) else 0
        }
    except Exception as e:
        log.error(f"计算DMI异常: {str(e)}")
        return {'pdi': 0, 'mdi': 0, 'adx': 0}

def update_four_level_sectors(context):
    """更新四层级板块管理"""
    try:
        # 1. 强势指数板块1 - 动态识别
        g.strong_sectors = identify_strong_sectors(context)
        log.info(f"强势指数板块1: {len(g.strong_sectors)}个")

        # 2. 强势板块中的股票2
        g.sector_stocks = get_sector_stocks(context, g.strong_sectors)
        log.info(f"强势板块中的股票2: {len(g.sector_stocks)}只")

        # 3. 板块中的强势股3
        g.strong_stocks_in_sector = identify_strong_stocks_in_sectors(context, g.sector_stocks)
        log.info(f"板块中的强势股3: {len(g.strong_stocks_in_sector)}只")

        # 4. 强势个股4
        g.strong_stocks_market = identify_strong_stocks_market(context)
        log.info(f"强势个股4: {len(g.strong_stocks_market)}只")

    except Exception as e:
        log.error(f"四层级板块更新异常: {str(e)}")

def identify_strong_sectors(context):
    """动态识别强势板块指数"""
    # 申万一级行业指数列表（这是候选池，不是强势板块）
    industry_candidates = [
        '801010.XSHE',  # 农林牧渔
        '801020.XSHE',  # 采掘
        '801030.XSHE',  # 化工
        '801040.XSHE',  # 钢铁
        '801050.XSHE',  # 有色金属
        '801080.XSHE',  # 电子
        '801110.XSHE',  # 家用电器
        '801120.XSHE',  # 食品饮料
        '801130.XSHE',  # 纺织服装
        '801140.XSHE',  # 轻工制造
        '801150.XSHE',  # 医药生物
        '801160.XSHE',  # 公用事业
        '801170.XSHE',  # 交通运输
        '801180.XSHE',  # 房地产
        '801200.XSHE',  # 商业贸易
        '801210.XSHE',  # 休闲服务
        '801230.XSHE',  # 综合
        '801710.XSHE',  # 建筑材料
        '801720.XSHE',  # 建筑装饰
        '801730.XSHE',  # 电气设备
        '801740.XSHE',  # 国防军工
        '801750.XSHE',  # 计算机
        '801760.XSHE',  # 传媒
        '801770.XSHE',  # 通信
        '801780.XSHE',  # 银行
        '801790.XSHE',  # 非银金融
        '801880.XSHE',  # 汽车
        '801890.XSHE'   # 机械设备
    ]
    
    # 概念板块候选池（聚宽平台的概念指数）
    concept_candidates = [
        '399006.XSHE',  # 创业板指
        '399005.XSHE',  # 中小板指
        # 可以添加更多概念指数
    ]
    
    # 合并所有候选板块
    all_sector_candidates = industry_candidates + concept_candidates
    
    strong_sectors = []
    sector_scores = []
    
    for sector in all_sector_candidates:
        try:
            # 获取板块指数数据
            sector_data = get_price(sector, count=20, end_date=context.previous_date,
                                  fields=['close', 'high', 'low', 'volume'])
            
            if len(sector_data) < 20:
                continue
            
            # 计算各项指标
            return_1d = (sector_data['close'].iloc[-1] / sector_data['close'].iloc[-2] - 1) * 100
            return_5d = (sector_data['close'].iloc[-1] / sector_data['close'].iloc[-6] - 1) * 100
            
            # 计算成交量放大倍数
            volume_ratio = sector_data['volume'].iloc[-1] / sector_data['volume'].rolling(5).mean().iloc[-1]
            
            # 计算均线
            ma5 = sector_data['close'].rolling(5).mean()
            ma10 = sector_data['close'].rolling(10).mean()
            
            # 计算DMI指标
            dmi_signals = calculate_dmi(sector_data)
            
            # 强势板块评分系统
            score = 0
            
            # 1. 涨幅评分
            if return_1d > 2.0:
                score += 30
            elif return_1d > 1.0:
                score += 20
            elif return_1d > 0:
                score += 10
            
            # 2. 5日涨幅评分
            if return_5d > 10.0:
                score += 25
            elif return_5d > 5.0:
                score += 15
            elif return_5d > 0:
                score += 5
            
            # 3. 成交量放大评分
            if volume_ratio > 2.0:
                score += 20
            elif volume_ratio > 1.5:
                score += 15
            elif volume_ratio > 1.2:
                score += 10
            
            # 4. 均线多头排列评分
            if (sector_data['close'].iloc[-1] > ma5.iloc[-1] and 
                ma5.iloc[-1] > ma10.iloc[-1] and
                ma5.iloc[-1] > ma5.iloc[-2]):
                score += 15
            
            # 5. DMI多头信号评分
            if (dmi_signals['pdi'] > dmi_signals['mdi'] and 
                dmi_signals['adx'] > 25):
                score += 10
            
            # 只有评分达到一定标准才认为是强势板块
            if score >= 50:  # 设定强势板块门槛
                sector_scores.append((sector, score))
                
        except Exception as e:
            log.error(f"分析板块{sector}异常: {str(e)}")
            continue
    
    # 按评分排序，选择前10个强势板块
    sector_scores.sort(key=lambda x: x[1], reverse=True)
    strong_sectors = [sector for sector, score in sector_scores[:10]]
    
    # 记录强势板块信息
    if strong_sectors:
        log.info(f"今日强势板块: {[sector + f'(评分:{score})' for sector, score in sector_scores[:5]]}")
    
    return strong_sectors

def get_sector_stocks(context, strong_sectors):
    """获取强势板块中的所有股票"""
    sector_stocks = []
    
    try:
        # 获取强势板块的成分股
        for sector in strong_sectors:
            try:
                # 这里需要根据板块获取成分股，聚宽平台可能需要使用其他API
                # 暂时使用全市场股票进行筛选
                pass
            except Exception as e:
                continue
        
        # 由于聚宽平台获取板块成分股的限制，这里使用全市场股票筛选
        all_stocks = list(get_all_securities(['stock']).index)
        
        # 基本过滤
        filtered_stocks = []
        for stock in all_stocks[:1000]:  # 限制处理数量
            try:
                # 基本过滤条件
                if (stock.startswith('000') or stock.startswith('002') or 
                    stock.startswith('300') or stock.startswith('600') or 
                    stock.startswith('601') or stock.startswith('603') or
                    stock.startswith('688')):
                    filtered_stocks.append(stock)
            except:
                continue
        
        sector_stocks = filtered_stocks[:500]  # 返回前500只作为候选
        
    except Exception as e:
        log.error(f"获取板块股票异常: {str(e)}")
    
    return sector_stocks

def identify_strong_stocks_in_sectors(context, sector_stocks):
    """从板块股票中识别强势个股"""
    strong_stocks = []
    
    # 基础过滤
    filtered_stocks = filter_stocks(sector_stocks)
    
    for stock in filtered_stocks[:200]:  # 限制处理数量
        try:
            stock_data = get_price(stock, count=20, end_date=context.previous_date,
                                 fields=['close', 'high', 'low', 'volume'])
            
            if len(stock_data) < 20:
                continue
            
            # 计算涨幅
            return_1d = (stock_data['close'].iloc[-1] / stock_data['close'].iloc[-2] - 1) * 100
            
            # 计算成交量放大倍数
            volume_ratio = stock_data['volume'].iloc[-1] / stock_data['volume'].rolling(5).mean().iloc[-1]
            
            # 计算5日均线
            ma5 = stock_data['close'].rolling(5).mean()
            
            # 计算DMI指标
            dmi_signals = calculate_dmi(stock_data)
            
            # 强势个股标准
            if (return_1d > 0.5 and  # 涨幅大于0.5%
                volume_ratio > 1.1 and  # 成交量放大10%以上
                stock_data['close'].iloc[-1] > ma5.iloc[-1] and  # 站稳5日均线
                ma5.iloc[-1] > ma5.iloc[-2] and  # 5日均线上行
                dmi_signals['pdi'] > dmi_signals['mdi']):  # DMI多头信号
                
                strong_stocks.append(stock)
                
        except Exception as e:
            continue
    
    return strong_stocks[:30]  # 限制数量

def identify_strong_stocks_market(context):
    """识别全市场强势个股"""
    all_stocks = list(get_all_securities(['stock']).index)
    strong_stocks = []
    
    # 基础过滤
    filtered_stocks = filter_stocks(all_stocks[:500])  # 限制处理数量
    
    for stock in filtered_stocks:
        try:
            stock_data = get_price(stock, count=20, end_date=context.previous_date,
                                 fields=['close', 'high', 'low', 'volume'])
            
            if len(stock_data) < 20:
                continue
            
            # 计算涨幅
            return_5d = (stock_data['close'].iloc[-1] / stock_data['close'].iloc[-6] - 1) * 100
            
            # 计算成交量放大倍数
            volume_ratio = stock_data['volume'].iloc[-1] / stock_data['volume'].rolling(10).mean().iloc[-1]
            
            # 计算均线
            ma5 = stock_data['close'].rolling(5).mean()
            ma10 = stock_data['close'].rolling(10).mean()
            
            # 计算DMI指标
            dmi_signals = calculate_dmi(stock_data)
            
            # 强势个股标准
            if (return_5d > 5.0 and  # 5日涨幅大于5%
                volume_ratio > 1.5 and  # 成交量放大50%以上
                ma5.iloc[-1] > ma10.iloc[-1] and  # 5日均线在10日均线之上
                dmi_signals['pdi'] > dmi_signals['mdi'] and  # DMI多头信号
                dmi_signals['adx'] > 30):  # ADX大于30
                
                strong_stocks.append(stock)
                
        except Exception as e:
            continue
    
    return strong_stocks[:20]  # 限制数量

def filter_stocks(stock_list):
    """股票基础过滤"""
    current_data = get_current_data()
    filtered_stocks = []
    
    for stock in stock_list:
        try:
            # 过滤停牌股票
            if current_data[stock].paused:
                continue
            
            # 过滤ST股票
            if 'ST' in current_data[stock].name or '*' in current_data[stock].name:
                continue
            
            # 过滤北交所
            if stock.startswith('8') or stock.startswith('4'):
                continue
            
            # 过滤黑名单
            if stock in g.blacklist or stock in g.just_sold:
                continue
            
            filtered_stocks.append(stock)
            
        except:
            continue
    
    return filtered_stocks

def check_positions(context):
    """检查持仓 - 动态止损止盈"""
    current_data = get_current_data()
    
    for stock in list(context.portfolio.positions.keys()):
        position = context.portfolio.positions[stock]
        
        if position.total_amount == 0:
            continue
        
        current_price = current_data[stock].last_price
        cost_price = position.avg_cost
        
        # 计算收益率
        return_pct = (current_price - cost_price) / cost_price
        
        # 获取股票技术指标
        try:
            stock_data = get_price(stock, count=20, end_date=context.previous_date,
                                 fields=['close', 'high', 'low', 'volume'])
            
            if len(stock_data) < 20:
                continue
            
            dmi_signals = calculate_dmi(stock_data)
            
            # 动态止损条件
            should_stop_loss = False
            
            # 1. DMI转空头信号（第一优先级）
            if dmi_signals['pdi'] < dmi_signals['mdi']:
                should_stop_loss = True
                log.info(f"DMI转空头止损: {stock}")
            
            # 2. 固定止损
            elif return_pct < -g.stop_loss_pct:
                should_stop_loss = True
                log.info(f"固定止损: {stock}, 亏损: {return_pct:.2%}")
            
            # 3. 跌破5日均线且放量
            else:
                ma5 = stock_data['close'].rolling(5).mean().iloc[-1]
                volume_ratio = stock_data['volume'].iloc[-1] / stock_data['volume'].rolling(5).mean().iloc[-1]
                
                if current_price < ma5 and volume_ratio > 1.5:
                    should_stop_loss = True
                    log.info(f"破位止损: {stock}")
            
            # 执行止损
            if should_stop_loss:
                smart_order_sell(context, stock, '止损')
                g.just_sold.append(stock)
                continue
            
            # 动态止盈条件
            should_take_profit = False
            
            # 1. DMI趋势减弱
            if dmi_signals['adx'] < 30 and return_pct > 0.05:
                should_take_profit = True
                log.info(f"DMI趋势减弱止盈: {stock}")
            
            # 2. 涨停打开立即卖出
            elif (current_data[stock].high_limit > 0 and
                  current_price < current_data[stock].high_limit * 0.99):
                should_take_profit = True
                log.info(f"涨停打开止盈: {stock}")
            
            # 执行止盈
            if should_take_profit:
                smart_order_sell(context, stock, '止盈')
                g.just_sold.append(stock)
                
        except Exception as e:
            log.error(f"检查持仓{stock}异常: {str(e)}")

def check_limit_up(context):
    """检查涨停股票"""
    current_data = get_current_data()
    
    for stock in list(context.portfolio.positions.keys()):
        position = context.portfolio.positions[stock]
        
        if position.total_amount == 0:
            continue
        
        # 检查是否涨停
        if (current_data[stock].high_limit > 0 and
            current_data[stock].last_price >= current_data[stock].high_limit * 0.995):
            log.info(f"持仓涨停: {stock}")
            # 涨停继续持有，不卖出

def adjust_positions(context):
    """动态调仓"""
    try:
        # 合并所有候选股票
        candidate_stocks = list(set(g.strong_stocks_in_sector + g.strong_stocks_market))
        
        # 最终选股
        final_stocks = candidate_stocks[:g.stock_num * 2]  # 选择更多候选
        
        # 按强度排序（简化处理）
        final_stocks = final_stocks[:g.stock_num]
        
        # 当前持仓
        current_positions = [stock for stock in context.portfolio.positions.keys() 
                            if context.portfolio.positions[stock].total_amount > 0]
        
        # 确定要卖出的股票
        stocks_to_sell = []
        for stock in current_positions:
            if stock not in final_stocks:
                stocks_to_sell.append(stock)
        
        # 确定要买入的股票
        stocks_to_buy = []
        for stock in final_stocks:
            if stock not in current_positions:
                stocks_to_buy.append(stock)
        
        # 执行卖出
        for stock in stocks_to_sell:
            smart_order_sell(context, stock, '调仓')
            log.info(f"调仓卖出: {stock}")
        
        # 执行买入
        if len(stocks_to_buy) > 0:
            # 计算可用资金
            available_cash = context.portfolio.available_cash * g.max_position_ratio
            target_value_per_stock = available_cash / len(stocks_to_buy)
            
            for stock in stocks_to_buy:
                smart_order_buy(context, stock, target_value_per_stock)
                log.info(f"调仓买入: {stock}, 目标金额: {target_value_per_stock:.0f}")
        
        # 清理刚卖出列表
        g.just_sold = []
        
    except Exception as e:
        log.error(f"调仓异常: {str(e)}")

def after_trading_end(context):
    """收盘后运行"""
    # 清理已完成的订单记录
    completed_orders = []
    for order_id in g.pending_orders:
        try:
            order = get_order(order_id)
            if order.status in [OrderStatus.held, OrderStatus.cancelled]:
                completed_orders.append(order_id)
        except:
            completed_orders.append(order_id)
    
    for order_id in completed_orders:
        if order_id in g.pending_orders:
            del g.pending_orders[order_id]
        if order_id in g.order_retry_count:
            del g.order_retry_count[order_id]
    
    # 记录当日情况
    log.info(f"当日结束，待处理订单数: {len(g.pending_orders)}")
    log.info(f"四层级板块统计 - 强势板块: {len(g.strong_sectors)}, 板块股票: {len(g.sector_stocks)}, 强势股: {len(g.strong_stocks_in_sector)}, 市场强势股: {len(g.strong_stocks_market)}")