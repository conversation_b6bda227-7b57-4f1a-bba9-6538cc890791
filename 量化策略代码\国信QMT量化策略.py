# -*- coding: utf-8 -*-
"""
国信证券QMT量化策略 - 基于郭海培老师《王者之声》和《胜者为王》的实战交易体系
策略核心：先大盘、再板块、再个股的多层级决策
作者：基于量化策略设计说明文档
"""

import numpy as np
import pandas as pd
import talib
from datetime import datetime, timedelta

# 策略参数
class Config:
    # 基础参数
    STOCK_NUM = 10  # 持仓股票数量
    MAX_POSITION_PCT = 0.1  # 单票最大仓位
    MAX_DRAWDOWN = 0.1  # 最大回撤
    
    # 技术指标参数
    KD_N = 9
    KD_M1 = 3
    KD_M2 = 3
    DMI_N = 14
    DMI_M = 6
    ENE_N = 25
    ENE_M1 = 6
    ENE_M2 = 6
    
    # 均线参数
    MA_SHORT = 5
    MA_MID = 20
    MA_LONG = 60
    
    # 风控参数
    STOP_LOSS_PCT = 0.08  # 止损比例
    TAKE_PROFIT_PCT = 0.15  # 止盈比例
    MONTHLY_DEVIATION_LIMIT = 0.15  # 月乖离限制
    
    # 选股参数
    MIN_MARKET_CAP = 50  # 最小市值（亿）
    MIN_TURNOVER_RATE = 0.02  # 最小换手率
    MAX_PE = 50  # 最大市盈率

class QMTStrategy:
    def __init__(self, context):
        """初始化策略"""
        self.context = context
        self.config = Config()
        
        # 股票池
        self.stock_pool = []
        self.tracking_pool = []  # 追踪池
        self.operation_pool = []  # 操作池
        
        # 市场状态
        self.market_state = 'sideways'
        
        # 目标仓位
        self.target_position = 0.5
        
        # 最大净值记录
        self.max_portfolio_value = 0
        self.last_portfolio_value = 0
        
        # 订单管理
        self.pending_orders = {}
        
        print("QMT策略初始化完成")
    
    def on_init(self):
        """策略初始化"""
        # 设置基准
        self.benchmark = '000300.SH'  # 沪深300
        
        # 设置交易参数
        self.set_commission(0.0003)  # 手续费
        self.set_slippage(0.002)  # 滑点
        
        # 初始化最大净值
        self.max_portfolio_value = self.get_portfolio_value()
        self.last_portfolio_value = self.get_portfolio_value()
        
        print("QMT策略初始化完成")
    
    def on_bar(self, data):
        """K线数据更新"""
        current_time = self.get_current_time()
        
        # 开盘时执行
        if current_time.hour == 9 and current_time.minute == 30:
            self.market_open()
        
        # 收盘前执行
        elif current_time.hour == 14 and current_time.minute == 50:
            self.market_close()
        
        # 每10分钟检查订单状态
        if current_time.minute % 10 == 0:
            self.check_order_status()
    
    def market_open(self):
        """开盘时运行"""
        # 判断市场状态
        self.market_state = self.judge_market_state()
        print(f"当前市场状态: {self.market_state}")
        
        # 根据市场状态调整仓位
        self.adjust_position_by_market_state()
        
        # 选股
        self.select_stocks()
        
        # 执行交易
        self.execute_trades()
    
    def market_close(self):
        """收盘前运行"""
        # 检查止损止盈
        self.check_stop_loss_profit()
    
    def judge_market_state(self):
        """判断市场状态：牛市/熊市/震荡市"""
        # 获取沪深300数据
        benchmark_data = self.get_history_data('000300.SH', 120, '1d')
        
        if benchmark_data is None or len(benchmark_data) < 120:
            return 'sideways'
        
        # 计算均线
        close_prices = np.array([bar['close'] for bar in benchmark_data])
        benchmark_data['MA5'] = talib.SMA(close_prices, 5)
        benchmark_data['MA10'] = talib.SMA(close_prices, 10)
        benchmark_data['MA20'] = talib.SMA(close_prices, 20)
        benchmark_data['MA60'] = talib.SMA(close_prices, 60)
        
        # 计算DMI指标
        benchmark_data = self.calculate_dmi(benchmark_data)
        
        # 获取当前数据
        current_price = close_prices[-1]
        ma60 = benchmark_data['MA60'][-1]
        ma60_slope = (benchmark_data['MA60'][-1] - benchmark_data['MA60'][-5]) / 5
        
        pdi = benchmark_data['PDI'][-1]
        mdi = benchmark_data['MDI'][-1]
        adx = benchmark_data['ADX'][-1]
        
        # 判断条件
        if (current_price > ma60 and ma60_slope > 0 and 
            pdi > mdi and adx > 55):
            return 'bull'
        elif (current_price < ma60 and ma60_slope < 0 and 
              mdi > pdi and adx > 55):
            return 'bear'
        else:
            return 'sideways'
    
    def calculate_kd(self, df, n=9, m1=3, m2=3):
        """计算KD指标"""
        high_prices = np.array([bar['high'] for bar in df])
        low_prices = np.array([bar['low'] for bar in df])
        close_prices = np.array([bar['close'] for bar in df])
        
        k, d = talib.STOCH(high_prices, low_prices, close_prices, 
                          fastk_period=n, slowk_period=m1, slowd_period=m2)
        
        return k, d
    
    def calculate_dmi(self, df, n=14, m=6):
        """计算DMI指标"""
        high_prices = np.array([bar['high'] for bar in df])
        low_prices = np.array([bar['low'] for bar in df])
        close_prices = np.array([bar['close'] for bar in df])
        
        pdi, mdi, adx = talib.PLUS_DI(high_prices, low_prices, close_prices, n), \
                       talib.MINUS_DI(high_prices, low_prices, close_prices, n), \
                       talib.ADX(high_prices, low_prices, close_prices, n)
        
        # 计算ADXR
        adxr = (adx + np.roll(adx, m)) / 2
        
        df['PDI'] = pdi
        df['MDI'] = mdi
        df['ADX'] = adx
        df['ADXR'] = adxr
        
        return df
    
    def calculate_ene(self, df, n=25, m1=6, m2=6):
        """计算ENE指标"""
        close_prices = np.array([bar['close'] for bar in df])
        ma = talib.SMA(close_prices, n)
        
        upper = (1 + m1/100) * ma
        lower = (1 - m2/100) * ma
        ene = (upper + lower) / 2
        
        df['ENE_UPPER'] = upper
        df['ENE_LOWER'] = lower
        df['ENE'] = ene
        
        return df
    
    def adjust_position_by_market_state(self):
        """根据市场状态调整仓位"""
        if self.market_state == 'bull':
            # 牛市：高仓位
            self.target_position = 0.8
        elif self.market_state == 'sideways':
            # 震荡市：中等仓位
            self.target_position = 0.5
        else:
            # 熊市：低仓位
            self.target_position = 0.2
    
    def select_stocks(self):
        """选股策略"""
        # 获取股票池
        stock_pool = self.get_stock_pool()
        
        # 根据市场状态选股
        if self.market_state == 'bull':
            selected_stocks = self.select_bull_stocks(stock_pool)
        elif self.market_state == 'bear':
            selected_stocks = self.select_bear_stocks(stock_pool)
        else:
            selected_stocks = self.select_sideways_stocks(stock_pool)
        
        # 更新操作池
        self.operation_pool = selected_stocks[:self.config.STOCK_NUM]
    
    def get_stock_pool(self):
        """获取基础股票池"""
        # 获取A股股票列表（这里需要根据QMT API调整）
        stocks = self.get_stock_list()
        
        # 过滤条件
        filtered_stocks = []
        
        for stock in stocks:
            try:
                # 获取基本面数据
                fundamental = self.get_fundamental_data(stock)
                if fundamental is None:
                    continue
                
                market_cap = fundamental.get('market_cap', 0)
                pe_ratio = fundamental.get('pe_ratio', 0)
                
                # 过滤条件
                if (market_cap > self.config.MIN_MARKET_CAP and 
                    pe_ratio < self.config.MAX_PE and 
                    pe_ratio > 0):
                    filtered_stocks.append(stock)
                    
            except:
                continue
        
        return filtered_stocks
    
    def select_bull_stocks(self, stock_pool):
        """牛市选股"""
        selected_stocks = []
        
        # 限制处理数量，避免超时
        max_stocks = min(100, len(stock_pool))
        stock_pool = stock_pool[:max_stocks]
        
        for stock in stock_pool:
            try:
                # 获取技术数据
                df = self.get_history_data(stock, 60, '1d')
                
                if df is None or len(df) < 60:
                    continue
                
                # 计算技术指标
                df = self.calculate_technical_indicators(df)
                
                # 牛市选股条件
                if self.is_bull_stock(df):
                    selected_stocks.append(stock)
                    
            except Exception as e:
                print(f"处理股票{stock}时出错: {str(e)}")
                continue
        
        return selected_stocks
    
    def select_bear_stocks(self, stock_pool):
        """熊市选股"""
        selected_stocks = []
        
        # 限制处理数量，避免超时
        max_stocks = min(100, len(stock_pool))
        stock_pool = stock_pool[:max_stocks]
        
        for stock in stock_pool:
            try:
                # 获取技术数据
                df = self.get_history_data(stock, 60, '1d')
                
                if df is None or len(df) < 60:
                    continue
                
                # 计算技术指标
                df = self.calculate_technical_indicators(df)
                
                # 熊市选股条件（防御性选股）
                if self.is_defensive_stock(df):
                    selected_stocks.append(stock)
                    
            except Exception as e:
                print(f"处理股票{stock}时出错: {str(e)}")
                continue
        
        return selected_stocks
    
    def select_sideways_stocks(self, stock_pool):
        """震荡市选股"""
        selected_stocks = []
        
        # 限制处理数量，避免超时
        max_stocks = min(100, len(stock_pool))
        stock_pool = stock_pool[:max_stocks]
        
        for stock in stock_pool:
            try:
                # 获取技术数据
                df = self.get_history_data(stock, 60, '1d')
                
                if df is None or len(df) < 60:
                    continue
                
                # 计算技术指标
                df = self.calculate_technical_indicators(df)
                
                # 震荡市选股条件
                if self.is_sideways_stock(df):
                    selected_stocks.append(stock)
                    
            except Exception as e:
                print(f"处理股票{stock}时出错: {str(e)}")
                continue
        
        return selected_stocks
    
    def calculate_technical_indicators(self, df):
        """计算技术指标"""
        close_prices = np.array([bar['close'] for bar in df])
        high_prices = np.array([bar['high'] for bar in df])
        low_prices = np.array([bar['low'] for bar in df])
        volume_prices = np.array([bar['volume'] for bar in df])
        
        # 计算均线
        df['MA5'] = talib.SMA(close_prices, 5)
        df['MA10'] = talib.SMA(close_prices, 10)
        df['MA20'] = talib.SMA(close_prices, 20)
        df['MA60'] = talib.SMA(close_prices, 60)
        
        # 计算KD指标
        df['K'], df['D'] = self.calculate_kd(df, self.config.KD_N, self.config.KD_M1, self.config.KD_M2)
        
        # 计算DMI指标
        df = self.calculate_dmi(df, self.config.DMI_N, self.config.DMI_M)
        
        # 计算ENE指标
        df = self.calculate_ene(df, self.config.ENE_N, self.config.ENE_M1, self.config.ENE_M2)
        
        # 计算量比
        df['VOLUME_RATIO'] = volume_prices / talib.SMA(volume_prices, 5)
        
        # 计算月乖离
        df['MONTHLY_DEVIATION'] = (close_prices - df['MA20']) / df['MA20'] * 100
        
        return df
    
    def is_bull_stock(self, df):
        """判断是否为牛市股票"""
        if df is None or len(df) < 60:
            return False
        
        current = df[-1]
        
        # 均线多头排列
        ma_condition = (current['MA5'] > current['MA10'] > 
                       current['MA20'] > current['MA60'])
        
        # DMI多头
        dmi_condition = (current['PDI'] > current['MDI'] and 
                        current['ADX'] > 30)
        
        # 量价配合
        volume_condition = current['volume'] > talib.SMA(np.array([bar['volume'] for bar in df]), 5)[-1]
        
        # KD金叉
        kd_condition = (current['K'] > current['D'] and 
                       df[-2]['K'] <= df[-2]['D'])
        
        return ma_condition and dmi_condition and volume_condition and kd_condition
    
    def is_defensive_stock(self, df):
        """判断是否为防御性股票"""
        if df is None or len(df) < 60:
            return False
        
        current = df[-1]
        
        # 基本面条件（简化处理）
        try:
            fundamental = self.get_fundamental_data(df[0]['code'])
            if fundamental is None:
                return False
            
            pe_ratio = fundamental.get('pe_ratio', 0)
            pb_ratio = fundamental.get('pb_ratio', 0)
            
            # 防御性条件
            fundamental_condition = (pe_ratio < 15 and pb_ratio < 1.5 and pe_ratio > 0)
        except:
            # 如果基本面数据获取失败，只考虑技术面
            fundamental_condition = True
        
        # 技术面条件
        technical_condition = (current['close'] > current['MA60'] and 
                              current['MA60'] > df[-5]['MA60'])
        
        return fundamental_condition and technical_condition
    
    def is_sideways_stock(self, df):
        """判断是否为震荡市股票"""
        if df is None or len(df) < 60:
            return False
        
        current = df[-1]
        
        # 均线纠结
        ma_convergence = (abs(current['MA5'] - current['MA10']) / current['MA10'] < 0.02 and
                         abs(current['MA10'] - current['MA20']) / current['MA20'] < 0.03)
        
        # 价格在均线附近
        price_near_ma = (abs(current['close'] - current['MA20']) / current['MA20'] < 0.05)
        
        # 量能适中
        volume_condition = (current['VOLUME_RATIO'] > 0.8 and current['VOLUME_RATIO'] < 3)
        
        return ma_convergence and price_near_ma and volume_condition
    
    def execute_trades(self):
        """执行交易"""
        # 获取当前持仓
        current_positions = self.get_positions()
        
        # 卖出不在操作池的股票
        for stock in current_positions:
            if stock not in self.operation_pool:
                # 使用买一价卖出
                current_data = self.get_market_data(stock)
                if current_data and 'buy1' in current_data:
                    sell_price = current_data['buy1']
                    self.sell(stock, current_positions[stock], sell_price)
                    print(f"卖出股票: {stock}, 价格: {sell_price}")
                else:
                    self.sell(stock, current_positions[stock])
                    print(f"卖出股票: {stock}")
        
        # 买入操作池中的股票
        if self.operation_pool:
            # 计算每只股票的仓位
            portfolio_value = self.get_portfolio_value()
            position_value = portfolio_value * self.target_position / len(self.operation_pool)
            
            for stock in self.operation_pool:
                current_data = self.get_market_data(stock)
                if current_data and 'sell1' in current_data:
                    # 使用卖一价买入
                    buy_price = current_data['sell1']
                    shares = int(position_value / buy_price)
                    if shares > 0:
                        # 确保是100的整数倍
                        shares = (shares // 100) * 100
                        if shares >= 100:
                            self.buy(stock, shares, buy_price)
                            print(f"买入股票: {stock}, 数量: {shares}, 价格: {buy_price}")
                else:
                    # 如果没有实时数据，使用市价单
                    shares = int(position_value / current_data['last'])
                    shares = (shares // 100) * 100
                    if shares >= 100:
                        self.buy(stock, shares)
                        print(f"买入股票: {stock}, 数量: {shares}")
    
    def check_order_status(self):
        """检查委托状态并处理未成交订单"""
        # 获取所有未成交订单
        open_orders = self.get_open_orders()
        
        for order in open_orders:
            # 检查订单是否超时（超过30分钟）
            order_time = order['created_time']
            current_time = self.get_current_time()
            
            if (current_time - order_time).total_seconds() > 1800:  # 30分钟
                # 撤销超时订单
                self.cancel_order(order['order_id'])
                print(f"撤销超时订单: {order['stock_code']}, 委托价格: {order['price']}")
                
                # 重新委托
                if order['direction'] == 'buy':
                    # 重新买入，提高价格
                    current_data = self.get_market_data(order['stock_code'])
                    if current_data and 'sell1' in current_data:
                        new_price = current_data['sell1'] * 1.01  # 提高1%
                        shares = int(order['amount'] / new_price)
                        shares = (shares // 100) * 100
                        if shares >= 100:
                            self.buy(order['stock_code'], shares, new_price)
                            print(f"重新买入: {order['stock_code']}, 数量: {shares}, 新价格: {new_price}")
                else:
                    # 重新卖出，降低价格
                    current_data = self.get_market_data(order['stock_code'])
                    if current_data and 'buy1' in current_data:
                        new_price = current_data['buy1'] * 0.99  # 降低1%
                        self.sell(order['stock_code'], order['amount'], new_price)
                        print(f"重新卖出: {order['stock_code']}, 新价格: {new_price}")
    
    def check_stop_loss_profit(self):
        """检查止损止盈"""
        positions = self.get_positions()
        
        for stock, amount in positions.items():
            position_cost = self.get_position_cost(stock)
            current_data = self.get_market_data(stock)
            
            if current_data and position_cost:
                current_price = current_data['last']
                # 计算盈亏比例
                profit_pct = (current_price - position_cost) / position_cost
                
                # 止损
                if profit_pct < -self.config.STOP_LOSS_PCT:
                    # 使用买一价快速止损
                    if 'buy1' in current_data:
                        sell_price = current_data['buy1'] * 0.99  # 降低1%确保成交
                        self.sell(stock, amount, sell_price)
                        print(f"止损卖出: {stock}, 亏损: {profit_pct:.2%}, 价格: {sell_price}")
                    else:
                        self.sell(stock, amount)
                        print(f"止损卖出: {stock}, 亏损: {profit_pct:.2%}")
                
                # 止盈
                elif profit_pct > self.config.TAKE_PROFIT_PCT:
                    # 使用买一价止盈
                    if 'buy1' in current_data:
                        sell_price = current_data['buy1']
                        self.sell(stock, amount, sell_price)
                        print(f"止盈卖出: {stock}, 盈利: {profit_pct:.2%}, 价格: {sell_price}")
                    else:
                        self.sell(stock, amount)
                        print(f"止盈卖出: {stock}, 盈利: {profit_pct:.2%}")
    
    def check_real_time_risk(self):
        """实时风控检查"""
        # 检查整体回撤
        portfolio_value = self.get_portfolio_value()
        if self.max_portfolio_value > 0:
            drawdown = (self.max_portfolio_value - portfolio_value) / self.max_portfolio_value
            if drawdown > self.config.MAX_DRAWDOWN:
                # 触发风控，减仓
                positions = self.get_positions()
                for stock in positions:
                    self.sell(stock, positions[stock])
                print(f"触发风控，整体回撤: {drawdown:.2%}")
        else:
            self.max_portfolio_value = portfolio_value
        
        # 更新最大净值
        if portfolio_value > self.max_portfolio_value:
            self.max_portfolio_value = portfolio_value
    
    def after_trading_end(self):
        """收盘后运行"""
        # 记录当日收益
        if self.last_portfolio_value > 0:
            daily_return = (self.get_portfolio_value() - self.last_portfolio_value) / self.last_portfolio_value
            print(f"当日收益率: {daily_return:.2%}")
        else:
            print("当日收益率: 首次运行")
        
        # 更新上次净值
        self.last_portfolio_value = self.get_portfolio_value()
        
        # 记录持仓
        positions = self.get_positions()
        if positions:
            print("当前持仓:")
            for stock, amount in positions.items():
                cost = self.get_position_cost(stock)
                current_price = self.get_market_data(stock)['last']
                print(f"{stock}: {amount}股, 成本: {cost:.2f}, 现价: {current_price:.2f}")
        else:
            print("当前无持仓")
    
    # QMT平台API接口（需要根据实际API调整）
    def get_stock_list(self):
        """获取股票列表"""
        # 这里需要根据QMT API实现
        return []
    
    def get_history_data(self, stock, count, period):
        """获取历史数据"""
        # 这里需要根据QMT API实现
        return None
    
    def get_market_data(self, stock):
        """获取实时行情"""
        # 这里需要根据QMT API实现
        return None
    
    def get_fundamental_data(self, stock):
        """获取基本面数据"""
        # 这里需要根据QMT API实现
        return None
    
    def get_positions(self):
        """获取当前持仓"""
        # 这里需要根据QMT API实现
        return {}
    
    def get_position_cost(self, stock):
        """获取持仓成本"""
        # 这里需要根据QMT API实现
        return 0
    
    def get_portfolio_value(self):
        """获取组合总价值"""
        # 这里需要根据QMT API实现
        return 0
    
    def get_current_time(self):
        """获取当前时间"""
        # 这里需要根据QMT API实现
        return datetime.now()
    
    def get_open_orders(self):
        """获取未成交订单"""
        # 这里需要根据QMT API实现
        return []
    
    def buy(self, stock, amount, price=None):
        """买入股票"""
        # 这里需要根据QMT API实现
        pass
    
    def sell(self, stock, amount, price=None):
        """卖出股票"""
        # 这里需要根据QMT API实现
        pass
    
    def cancel_order(self, order_id):
        """撤销订单"""
        # 这里需要根据QMT API实现
        pass
    
    def set_commission(self, rate):
        """设置手续费"""
        # 这里需要根据QMT API实现
        pass
    
    def set_slippage(self, rate):
        """设置滑点"""
        # 这里需要根据QMT API实现
        pass

# 策略说明
"""
本策略基于郭海培老师《王者之声》和《胜者为王》的实战交易体系，实现"先大盘、再板块、再个股"的多层级决策。

核心特点：
1. 市场状态判断：基于60日均线、DMI指标判断牛市/熊市/震荡市
2. 动态仓位管理：根据市场状态调整整体仓位
3. 多因子选股：结合技术面、基本面、资金面选股
4. 严格风控：动态止损止盈、整体回撤控制
5. 技术指标：KD、DMI、ENE、均线系统等
6. 智能委托：使用卖一价买入、买一价卖出，提高成交率
7. 订单管理：超时订单自动撤销并重新委托

交易执行要点：
1. 买入：使用卖一价（sell1）买入，确保成交
2. 卖出：使用买一价（buy1）卖出，确保成交
3. 止损：使用买一价*0.99快速止损
4. 止盈：使用买一价止盈
5. 订单：按金额下单，避免数量计算错误
6. 超时处理：30分钟未成交订单自动撤销并重新委托

使用方法：
1. 在国信证券QMT平台创建策略
2. 复制本代码到策略编辑器
3. 根据QMT API调整相关接口函数
4. 设置参数（可根据需要调整Config类中的参数）
5. 运行回测或实盘

注意事项：
1. 请根据QMT平台的实际API调整相关接口函数
2. 请根据实际情况调整参数
3. 建议先进行回测验证
4. 实盘前请充分测试
5. 注意风险控制
6. 使用实时买卖价格，提高成交率
7. 定期检查订单状态，及时处理超时订单
""" 