# -*- coding: utf-8 -*-
"""
国信证券QMT量化策略 - 基于四层级板块管理的多层级决策体系
策略核心：先大盘、再板块、再个股 + 智能委托管理
融合郭海培老师《王者之声》和《胜者为王》的实战交易体系
"""

import numpy as np
import pandas as pd
import talib
from datetime import datetime, timedelta
import time
import threading
from xtquant import xtdata, xttrader

class QMTStrategy:
    def __init__(self):
        """初始化策略"""
        # 策略参数
        self.stock_num = 10  # 持股数量
        self.max_position_pct = 0.1  # 单票最大仓位
        self.stop_loss_pct = 0.08  # 止损比例
        self.take_profit_pct = 0.15  # 止盈比例

        # 四层级板块管理
        self.strong_sectors = []  # 强势指数板块1
        self.sector_stocks = []   # 强势板块中的股票2
        self.strong_stocks_in_sector = []  # 板块中的强势股3
        self.strong_stocks_market = []     # 强势个股4

        # 委托管理 - 核心改进
        self.pending_orders = {}  # 待处理订单 {order_id: {'time': datetime, 'stock': code, 'retry_count': int}}
        self.order_retry_count = {}  # 订单重试次数
        self.max_retry = 3  # 最大重试次数
        self.order_check_interval = 3  # 检查间隔（秒）

        # 黑名单管理
        self.blacklist = []  # 黑名单股票
        self.just_sold = []  # 刚卖出的股票

        # 市场风险控制
        self.market_risk_level = 'medium'
        self.max_position_ratio = 0.6

        # 初始化交易接口
        self.xt_trader = xttrader.XtQuantTrader()
        self.account = None  # 需要设置实际账户

        # 启动委托监控线程
        self.start_order_monitor()

        print("QMT策略初始化完成")

    def start_order_monitor(self):
        """启动委托监控线程 - 3秒检查机制"""
        def monitor_orders():
            while True:
                try:
                    if self.is_trading_time():
                        self.check_order_status()
                    time.sleep(self.order_check_interval)
                except Exception as e:
                    print(f"委托监控异常: {e}")
                    time.sleep(1)

        self.order_check_thread = threading.Thread(target=monitor_orders, daemon=True)
        self.order_check_thread.start()

    def is_trading_time(self):
        """判断是否为交易时间"""
        now = datetime.now()
        current_time = now.time()
        
        # 交易时间：9:30-11:30, 13:00-15:00
        morning_start = datetime.strptime('09:30:00', '%H:%M:%S').time()
        morning_end = datetime.strptime('11:30:00', '%H:%M:%S').time()
        afternoon_start = datetime.strptime('13:00:00', '%H:%M:%S').time()
        afternoon_end = datetime.strptime('15:00:00', '%H:%M:%S').time()
        
        return ((morning_start <= current_time <= morning_end) or 
                (afternoon_start <= current_time <= afternoon_end))

    def check_order_status(self):
        """检查委托状态 - 3秒检查机制"""
        try:
            # 获取未成交订单
            orders = self.xt_trader.query_stock_orders(self.account)
            current_time = datetime.now()

            for order in orders:
                if order.order_status not in [xttrader.ORDER_FILLED, xttrader.ORDER_CANCELLED]:
                    order_id = order.order_id
                    stock_code = order.stock_code

                    # 检查订单是否在监控列表中
                    if order_id not in self.pending_orders:
                        # 新订单，加入监控
                        self.pending_orders[order_id] = {
                            'time': current_time,
                            'stock': stock_code,
                            'retry_count': 0
                        }
                        continue

                    # 检查订单是否超过3秒未成交
                    elapsed_time = (current_time - self.pending_orders[order_id]['time']).total_seconds()

                    if elapsed_time >= self.order_check_interval:  # 3秒未成交
                        print(f"订单超时未成交: {stock_code}, 订单ID: {order_id}, 已等待: {elapsed_time:.1f}秒")
                        
                        # 撤单
                        cancel_result = self.xt_trader.cancel_order_stock(self.account, order_id)
                        if cancel_result == 0:  # 撤单成功
                            print(f"成功撤销订单: {stock_code}, 订单ID: {order_id}")
                            
                            # 重新下单
                            self.retry_order(stock_code, order_id, order)
                        else:
                            print(f"撤单失败: {stock_code}, 订单ID: {order_id}")

                        # 清理记录
                        if order_id in self.pending_orders:
                            del self.pending_orders[order_id]

        except Exception as e:
            print(f"检查订单状态异常: {e}")

    def retry_order(self, stock_code, original_order_id, original_order):
        """重新下单 - 智能价格调整"""
        try:
            # 检查重试次数
            if original_order_id not in self.order_retry_count:
                self.order_retry_count[original_order_id] = 0

            self.order_retry_count[original_order_id] += 1

            # 超过最大重试次数
            if self.order_retry_count[original_order_id] > self.max_retry:
                print(f"订单重试次数超限: {stock_code}, 放弃重试")
                return

            # 获取实时行情
            tick_data = xtdata.get_full_tick([stock_code])
            if not tick_data or stock_code not in tick_data:
                print(f"获取行情失败: {stock_code}")
                return

            current_price = tick_data[stock_code]['lastPrice']
            retry_count = self.order_retry_count[original_order_id]

            if original_order.order_type == xttrader.STOCK_BUY:  # 买入订单
                # 逐步提高买入价格
                price_adjustment = 1 + (retry_count * 0.005)  # 每次提高0.5%
                new_price = current_price * price_adjustment

                # 限制在涨停价以内
                limit_up_price = tick_data[stock_code]['upperLimit']
                if limit_up_price > 0:
                    new_price = min(new_price, limit_up_price * 0.995)

                # 重新买入
                order_id = self.xt_trader.order_stock(
                    self.account, stock_code, xttrader.STOCK_BUY, 
                    original_order.order_volume, xttrader.FIX_PRICE, new_price
                )

                if order_id > 0:
                    print(f"重新买入: {stock_code}, 第{retry_count}次重试, 新价格: {new_price:.2f}")
                    # 记录新订单
                    self.pending_orders[order_id] = {
                        'time': datetime.now(),
                        'stock': stock_code,
                        'retry_count': retry_count
                    }
                else:
                    print(f"重新买入失败: {stock_code}")

            else:  # 卖出订单
                # 逐步降低卖出价格
                price_adjustment = 1 - (retry_count * 0.005)  # 每次降低0.5%
                new_price = current_price * price_adjustment

                # 限制在跌停价以上
                limit_down_price = tick_data[stock_code]['lowerLimit']
                if limit_down_price > 0:
                    new_price = max(new_price, limit_down_price * 1.005)

                # 重新卖出
                order_id = self.xt_trader.order_stock(
                    self.account, stock_code, xttrader.STOCK_SELL, 
                    original_order.order_volume, xttrader.FIX_PRICE, new_price
                )

                if order_id > 0:
                    print(f"重新卖出: {stock_code}, 第{retry_count}次重试, 新价格: {new_price:.2f}")
                    # 记录新订单
                    self.pending_orders[order_id] = {
                        'time': datetime.now(),
                        'stock': stock_code,
                        'retry_count': retry_count
                    }
                else:
                    print(f"重新卖出失败: {stock_code}")

        except Exception as e:
            print(f"重新下单异常: {stock_code}, 错误: {str(e)}")

    def smart_order_buy(self, stock_code, target_value):
        """智能买入下单"""
        try:
            # 获取实时行情
            tick_data = xtdata.get_full_tick([stock_code])
            if not tick_data or stock_code not in tick_data:
                print(f"获取行情失败，跳过买入: {stock_code}")
                return None

            current_price = tick_data[stock_code]['lastPrice']
            
            # 买入策略：使用涨停价*0.99委托，确保能够成交
            limit_up_price = tick_data[stock_code]['upperLimit']
            if limit_up_price > 0:
                buy_price = limit_up_price * 0.99
            else:
                buy_price = current_price * 1.01

            # 计算买入数量（必须是100的整数倍）
            volume = int(target_value / buy_price)
            volume = (volume // 100) * 100

            if volume < 100:
                print(f"买入数量不足100股，跳过: {stock_code}")
                return None

            # 下单
            order_id = self.xt_trader.order_stock(
                self.account, stock_code, xttrader.STOCK_BUY, 
                volume, xttrader.FIX_PRICE, buy_price
            )

            if order_id > 0:
                # 记录订单到监控列表
                self.pending_orders[order_id] = {
                    'time': datetime.now(),
                    'stock': stock_code,
                    'retry_count': 0
                }
                print(f"智能买入下单: {stock_code}, 数量: {volume}, 委托价格: {buy_price:.2f}")
                return order_id
            else:
                print(f"买入下单失败: {stock_code}")
                return None

        except Exception as e:
            print(f"智能买入下单异常: {stock_code}, 错误: {str(e)}")
            return None

    def smart_order_sell(self, stock_code, volume, reason='调仓'):
        """智能卖出下单"""
        try:
            # 获取实时行情
            tick_data = xtdata.get_full_tick([stock_code])
            if not tick_data or stock_code not in tick_data:
                print(f"获取行情失败，跳过卖出: {stock_code}")
                return None

            current_price = tick_data[stock_code]['lastPrice']
            
            # 卖出策略：根据原因选择不同价格
            if reason == '止损':
                # 止损：使用跌停价*1.01，确保快速成交
                limit_down_price = tick_data[stock_code]['lowerLimit']
                if limit_down_price > 0:
                    sell_price = limit_down_price * 1.01
                else:
                    sell_price = current_price * 0.95
            else:
                # 正常卖出：使用跌停价*1.01
                limit_down_price = tick_data[stock_code]['lowerLimit']
                if limit_down_price > 0:
                    sell_price = limit_down_price * 1.01
                else:
                    sell_price = current_price * 0.99

            # 下单
            order_id = self.xt_trader.order_stock(
                self.account, stock_code, xttrader.STOCK_SELL, 
                volume, xttrader.FIX_PRICE, sell_price
            )

            if order_id > 0:
                # 记录订单到监控列表
                self.pending_orders[order_id] = {
                    'time': datetime.now(),
                    'stock': stock_code,
                    'retry_count': 0
                }
                print(f"智能卖出下单: {stock_code}, 数量: {volume}, 原因: {reason}, 委托价格: {sell_price:.2f}")
                return order_id
            else:
                print(f"卖出下单失败: {stock_code}")
                return None

        except Exception as e:
            print(f"智能卖出下单异常: {stock_code}, 错误: {str(e)}")
            return None

    def market_risk_assessment(self):
        """市场风险评估 - 基于沪深300和富时A50期指"""
        try:
            # 获取沪深300指数数据
            hs300_data = xtdata.get_market_data(['000300.SH'], period='1d', count=60)

            if hs300_data is None or len(hs300_data) < 60:
                return

            # 计算60日均线
            hs300_close = hs300_data['000300.SH']['close']
            ma60 = hs300_close.rolling(60).mean()
            current_price = hs300_close.iloc[-1]
            current_ma60 = ma60.iloc[-1]
            prev_ma60 = ma60.iloc[-2]

            # 计算DMI指标
            dmi_signals = self.calculate_dmi(hs300_data['000300.SH'])

            # 市场风险判断
            if (current_price > current_ma60 and current_ma60 > prev_ma60 and
                dmi_signals['pdi'] > dmi_signals['mdi'] and dmi_signals['adx'] > 55):
                self.market_risk_level = 'low'
                self.max_position_ratio = 1.0
            elif current_price > current_ma60 * 0.98:
                self.market_risk_level = 'medium'
                self.max_position_ratio = 0.6
            else:
                self.market_risk_level = 'high'
                self.max_position_ratio = 0.2

            print(f"市场风险等级: {self.market_risk_level}, 最大仓位: {self.max_position_ratio}")

        except Exception as e:
            print(f"市场风险评估异常: {e}")

    def calculate_dmi(self, stock_data, n=14):
        """计算DMI指标"""
        try:
            high = stock_data['high'].values
            low = stock_data['low'].values
            close = stock_data['close'].values
            
            pdi = talib.PLUS_DI(high, low, close, timeperiod=n)
            mdi = talib.MINUS_DI(high, low, close, timeperiod=n)
            adx = talib.ADX(high, low, close, timeperiod=n)
            
            return {
                'pdi': pdi[-1] if len(pdi) > 0 else 0,
                'mdi': mdi[-1] if len(mdi) > 0 else 0,
                'adx': adx[-1] if len(adx) > 0 else 0
            }
        except:
            return {'pdi': 0, 'mdi': 0, 'adx': 0}

    def run_strategy(self):
        """运行策略主循环"""
        print("策略开始运行...")
        
        while True:
            try:
                current_time = datetime.now()
                
                # 开盘时执行
                if current_time.hour == 9 and current_time.minute == 5:
                    self.market_risk_assessment()
                    self.update_four_level_sectors()
                
                # 盘中检查
                elif current_time.hour == 9 and current_time.minute == 30:
                    self.check_positions()
                
                # 收盘前调仓
                elif current_time.hour == 14 and current_time.minute == 50:
                    self.adjust_positions()
                
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                print(f"策略运行异常: {e}")
                time.sleep(60)

    def update_four_level_sectors(self):
        """更新四层级板块管理"""
        try:
            print("开始更新四层级板块...")
            
            # 1. 强势指数板块1
            self.strong_sectors = self.identify_strong_sectors()
            print(f"强势指数板块1: {len(self.strong_sectors)}个")

            # 2. 强势板块中的股票2
            self.sector_stocks = self.get_sector_stocks()
            print(f"强势板块中的股票2: {len(self.sector_stocks)}只")

            # 3. 板块中的强势股3
            self.strong_stocks_in_sector = self.identify_strong_stocks_in_sectors()
            print(f"板块中的强势股3: {len(self.strong_stocks_in_sector)}只")

            # 4. 强势个股4
            self.strong_stocks_market = self.identify_strong_stocks_market()
            print(f"强势个股4: {len(self.strong_stocks_market)}只")

        except Exception as e:
            print(f"四层级板块更新异常: {e}")

    def identify_strong_sectors(self):
        """识别强势板块指数"""
        # QMT平台的板块指数代码
        sectors = [
            '801010.SH',  # 农林牧渔
            '801020.SH',  # 采掘
            '801030.SH',  # 化工
            '801040.SH',  # 钢铁
            '801050.SH',  # 有色金属
            '801080.SH',  # 电子
            '801110.SH',  # 家用电器
            '801120.SH',  # 食品饮料
            '801130.SH',  # 纺织服装
            '801140.SH',  # 轻工制造
            '801150.SH',  # 医药生物
            '801160.SH',  # 公用事业
            '801170.SH',  # 交通运输
            '801180.SH',  # 房地产
            '801200.SH',  # 商业贸易
            '801210.SH',  # 休闲服务
            '801230.SH',  # 综合
            '801710.SH',  # 建筑材料
            '801720.SH',  # 建筑装饰
            '801730.SH',  # 电气设备
            '801740.SH',  # 国防军工
            '801750.SH',  # 计算机
            '801760.SH',  # 传媒
            '801770.SH',  # 通信
            '801780.SH',  # 银行
            '801790.SH',  # 非银金融
            '801880.SH',  # 汽车
            '801890.SH'   # 机械设备
        ]
        
        strong_sectors = []
        
        try:
            # 获取板块数据
            sector_data = xtdata.get_market_data(sectors, period='1d', count=20)
            
            for sector in sectors:
                if sector not in sector_data or len(sector_data[sector]) < 20:
                    continue
                
                data = sector_data[sector]
                
                # 计算涨幅
                return_1d = (data['close'].iloc[-1] / data['close'].iloc[-2] - 1) * 100
                
                # 计算成交量放大倍数
                volume_ratio = data['volume'].iloc[-1] / data['volume'].rolling(5).mean().iloc[-1]
                
                # 计算5日均线
                ma5 = data['close'].rolling(5).mean()
                
                # 强势板块标准
                if (return_1d > 1.0 and  # 涨幅大于1%
                    volume_ratio > 1.2 and  # 成交量放大20%以上
                    data['close'].iloc[-1] > ma5.iloc[-1] and  # 站稳5日均线
                    ma5.iloc[-1] > ma5.iloc[-2]):  # 5日均线上行
                    
                    strong_sectors.append(sector)
                    
        except Exception as e:
            print(f"识别强势板块异常: {e}")
        
        return strong_sectors[:10]  # 限制数量

    def get_sector_stocks(self):
        """获取强势板块中的所有股票"""
        sector_stocks = []
        
        try:
            # 获取A股股票列表
            all_stocks = xtdata.get_stock_list_in_sector('沪深A股')
            
            # 基本过滤
            for stock in all_stocks[:500]:  # 限制处理数量
                if (stock.endswith('.SH') or stock.endswith('.SZ')) and not stock.startswith('688'):
                    sector_stocks.append(stock)
                    
        except Exception as e:
            print(f"获取板块股票异常: {e}")
        
        return sector_stocks[:200]  # 返回前200只

    def identify_strong_stocks_in_sectors(self):
        """从板块股票中识别强势个股"""
        strong_stocks = []
        
        # 基础过滤
        filtered_stocks = self.filter_stocks(self.sector_stocks)
        
        try:
            # 获取股票数据
            stock_data = xtdata.get_market_data(filtered_stocks[:100], period='1d', count=20)
            
            for stock in filtered_stocks[:100]:
                if stock not in stock_data or len(stock_data[stock]) < 20:
                    continue
                
                data = stock_data[stock]
                
                # 计算涨幅
                return_1d = (data['close'].iloc[-1] / data['close'].iloc[-2] - 1) * 100
                
                # 计算成交量放大倍数
                volume_ratio = data['volume'].iloc[-1] / data['volume'].rolling(5).mean().iloc[-1]
                
                # 计算5日均线
                ma5 = data['close'].rolling(5).mean()
                
                # 计算DMI指标
                dmi_signals = self.calculate_dmi(data)
                
                # 强势个股标准
                if (return_1d > 0.5 and  # 涨幅大于0.5%
                    volume_ratio > 1.1 and  # 成交量放大10%以上
                    data['close'].iloc[-1] > ma5.iloc[-1] and  # 站稳5日均线
                    ma5.iloc[-1] > ma5.iloc[-2] and  # 5日均线上行
                    dmi_signals['pdi'] > dmi_signals['mdi']):  # DMI多头信号
                    
                    strong_stocks.append(stock)
                    
        except Exception as e:
            print(f"识别板块强势股异常: {e}")
        
        return strong_stocks[:30]  # 限制数量

    def identify_strong_stocks_market(self):
        """识别全市场强势个股"""
        strong_stocks = []
        
        try:
            # 获取A股股票列表
            all_stocks = xtdata.get_stock_list_in_sector('沪深A股')
            
            # 基础过滤
            filtered_stocks = self.filter_stocks(all_stocks[:200])  # 限制处理数量
            
            # 获取股票数据
            stock_data = xtdata.get_market_data(filtered_stocks, period='1d', count=20)
            
            for stock in filtered_stocks:
                if stock not in stock_data or len(stock_data[stock]) < 20:
                    continue
                
                data = stock_data[stock]
                
                # 计算涨幅
                return_5d = (data['close'].iloc[-1] / data['close'].iloc[-6] - 1) * 100
                
                # 计算成交量放大倍数
                volume_ratio = data['volume'].iloc[-1] / data['volume'].rolling(10).mean().iloc[-1]
                
                # 计算均线
                ma5 = data['close'].rolling(5).mean()
                ma10 = data['close'].rolling(10).mean()
                
                # 计算DMI指