# 聚宽量化策略修复说明

## 问题分析

### 1. 图片中的错误
- **错误信息**：`开仓数量不能小于100` 和 `开仓数量必须是100的整数倍` 矛盾
- **根本原因**：使用 `order_target()` 函数时，数量计算出现错误
- **具体表现**：计算出的股票数量不是100的整数倍，导致委托失败

### 2. 参考文件分析
通过分析 `3、5年15倍的收益，年化79.93，可实盘，拿走不谢！.txt` 发现：
- 使用 `order_value()` 函数按金额下单，而不是按数量下单
- 避免了数量计算的复杂性
- 系统自动处理100股整数倍的问题

## 解决方案

### 1. 交易执行策略优化

**买卖价格策略**：
- **买入**：使用卖一价（sell1）买入，确保能够成交
- **卖出**：使用买一价（buy1）卖出，确保能够成交
- **止损**：使用买一价*0.99快速止损，确保成交
- **止盈**：使用买一价止盈，获得较好价格

**订单管理策略**：
- **避免数量错误**：使用 `order_value()` 函数按金额下单
- **订单超时处理**：5分钟未成交订单自动撤销并重新委托（最快时间）
- **重新委托策略**：使用市价单重新委托，确保成交
- **检查频率**：每5分钟检查一次订单状态，确保快速处理

### 2. 代码修改要点

**原代码问题**：
```python
# 错误的做法
shares = int(position_value / buy_price)
shares = (shares // 100) * 100
if shares >= 100:
    order_target(stock, shares, pindex=0, price=buy_price)
```

**修复后代码**：
```python
# 正确的做法 - 简化版本
order_value(stock, position_value)  # 让平台自动处理价格和数量
```

### 3. 聚宽平台特殊注意事项

- 使用 `order_value(security, value, price=buy_price)` 按金额买入
- 使用 `order_target_percent(security, 0, price=sell_price)` 按比例卖出
- 避免使用 `order_target()` 的数量计算
- 定期检查未成交订单状态，及时处理超时订单

## 修复效果

1. **解决委托失败问题**：使用正确的价格和订单函数
2. **提高成交率**：使用实时买卖价格，简化下单逻辑
3. **避免数量错误**：按金额下单，系统自动处理数量
4. **增强稳定性**：订单超时自动处理机制
5. **提高处理速度**：5分钟超时，每5分钟检查，确保快速响应
6. **简化逻辑**：减少复杂的价格计算，提高成功率

## 设计说明更新

已将以下内容添加到 `量化策略设计说明.md`：

1. **交易执行关键要点**：详细的买卖价格策略和订单管理策略
2. **聚宽平台特殊注意事项**：平台特定的API使用说明
3. **常见错误及解决方案**：具体的错误类型和解决方法

## 总结

通过分析参考文件和错误信息，成功解决了聚宽策略的委托问题。关键改进包括：

1. 使用 `order_value()` 替代 `order_target()` 避免数量计算错误
2. 简化下单逻辑，让平台自动处理价格和数量
3. 将订单超时时间从30分钟缩短到5分钟，提高响应速度
4. 使用市价单重新委托，确保成交
5. 每5分钟检查订单状态，确保快速处理
6. 更新设计说明文档，确保后续开发不会出现同样问题

这些修复确保了策略在聚宽平台上的稳定运行和高效成交，解决了用户反馈的"委托单买不到"和"30分钟超时太长"的问题。 